-- 添加唯一性约束防止重复采集
-- 创建时间: 2025-06-30
-- 目的: 防止重复磁力链接和下载任务

-- 1. 首先清理现有的重复数据

-- 清理重复的磁力链接（保留ID最小的记录）
DELETE FROM jav_magnets 
WHERE id NOT IN (
    SELECT MIN(id) 
    FROM (
        SELECT id, magnet_url 
        FROM jav_magnets
    ) AS temp_table
    GROUP BY magnet_url
);

-- 清理重复的下载任务（保留ID最小的记录）
DELETE FROM download_tasks 
WHERE id NOT IN (
    SELECT MIN(id) 
    FROM (
        SELECT id, magnet_uri 
        FROM download_tasks
    ) AS temp_table
    GROUP BY magnet_uri
);

-- 2. 添加唯一性约束

-- 为磁力链接URL添加唯一性约束
ALTER TABLE jav_magnets 
ADD CONSTRAINT uk_jav_magnets_magnet_url 
UNIQUE (magnet_url);

-- 为下载任务的磁力链接添加唯一性约束
ALTER TABLE download_tasks 
ADD CONSTRAINT uk_download_tasks_magnet_uri 
UNIQUE (magnet_uri);

-- 3. 添加复合唯一性约束（影片+磁力链接）
-- 防止同一影片的相同磁力链接被重复添加
ALTER TABLE jav_magnets 
ADD CONSTRAINT uk_jav_magnets_movie_magnet 
UNIQUE (movie_id, magnet_url);

-- 4. 添加索引优化查询性能

-- 为磁力链接URL添加索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_jav_magnets_magnet_url ON jav_magnets(magnet_url);

-- 为下载任务磁力链接添加索引
CREATE INDEX IF NOT EXISTS idx_download_tasks_magnet_uri ON download_tasks(magnet_uri);

-- 为下载任务状态添加索引
CREATE INDEX IF NOT EXISTS idx_download_tasks_status ON download_tasks(status);

-- 5. 添加注释说明
COMMENT ON CONSTRAINT uk_jav_magnets_magnet_url ON jav_magnets IS '磁力链接URL唯一性约束';
COMMENT ON CONSTRAINT uk_download_tasks_magnet_uri ON download_tasks IS '下载任务磁力链接唯一性约束';
COMMENT ON CONSTRAINT uk_jav_magnets_movie_magnet ON jav_magnets IS '影片+磁力链接复合唯一性约束';