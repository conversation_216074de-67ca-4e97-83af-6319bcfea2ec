#!/usr/bin/env python3
"""
JavSP HTTP包装器服务
将JavSP的功能包装为REST API
"""

import os
import sys
import json
import logging
import threading
import traceback
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS

# 添加JavSP模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入JavSP模块
try:
    from javsp.datatype import MovieInfo
    from javsp.config import Cfg
    from javsp.avid import get_id
    from javsp import web
except ImportError as e:
    print(f"导入JavSP模块失败: {e}")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 全局变量
crawlers_imported = False
config_loaded = False

def init_javsp():
    """初始化JavSP"""
    global crawlers_imported, config_loaded

    try:
        # 加载配置
        if not config_loaded:
            Cfg()
            config_loaded = True
            logger.info("JavSP配置加载成功")

        # 手动导入常用爬虫模块
        if not crawlers_imported:
            try:
                import javsp.web.javdb
                import javsp.web.javlibrary
                import javsp.web.avsox
                crawlers_imported = True
                logger.info("JavSP爬虫模块导入成功")
            except ImportError as e:
                logger.warning(f"部分爬虫模块导入失败: {e}")
                crawlers_imported = True  # 继续运行，即使部分模块失败

    except Exception as e:
        logger.error(f"初始化JavSP失败: {e}")
        raise

def get_movie_info_by_code(code):
    """根据番号获取影片信息"""
    try:
        # 初始化JavSP
        init_javsp()
        
        # 识别番号类型
        movie = get_id(code)
        if not movie:
            return None, "无法识别的番号格式"
        
        logger.info(f"开始获取影片信息: {code} -> {movie.dvdid or movie.cid}")
        
        # 创建MovieInfo实例
        if movie.dvdid:
            movie_info = MovieInfo(movie.dvdid)
        elif movie.cid:
            movie_info = MovieInfo(cid=movie.cid)
        else:
            return None, "无法创建影片信息实例"
        
        # 获取爬虫配置
        crawler_mods = Cfg().crawler.selection[movie.data_src]
        if not crawler_mods:
            return None, f"没有为数据源 {movie.data_src} 配置爬虫"
        
        # 使用多线程抓取数据
        all_info = {}
        thread_pool = []
        
        for crawler_id in crawler_mods:
            try:
                mod_name = f"javsp.web.{crawler_id.value}"
                if mod_name in sys.modules:
                    mod = sys.modules[mod_name]
                    if hasattr(mod, 'parse_data'):
                        info = MovieInfo(movie.dvdid) if movie.dvdid else MovieInfo(cid=movie.cid)
                        all_info[crawler_id.value] = info
                        
                        def wrapper(parser, info_obj):
                            try:
                                parser(info_obj)
                            except Exception as e:
                                logger.warning(f"爬虫 {crawler_id.value} 执行失败: {e}")
                        
                        th = threading.Thread(
                            target=wrapper, 
                            args=(mod.parse_data, info),
                            name=f"crawler-{crawler_id.value}"
                        )
                        th.start()
                        thread_pool.append(th)
                        
            except Exception as e:
                logger.warning(f"加载爬虫 {crawler_id.value} 失败: {e}")
        
        # 等待所有线程完成
        timeout = Cfg().network.retry * Cfg().network.timeout.total_seconds()
        for th in thread_pool:
            th.join(timeout=timeout)
        
        # 合并数据
        merged_info = merge_movie_info(all_info)
        
        if merged_info:
            return merged_info, None
        else:
            return None, "未能获取到有效的影片信息"
            
    except Exception as e:
        logger.error(f"获取影片信息时发生错误: {e}")
        logger.error(traceback.format_exc())
        return None, str(e)

def merge_movie_info(all_info):
    """合并多个数据源的影片信息"""
    if not all_info:
        return None
    
    # 选择第一个有效的信息作为基础
    base_info = None
    for info in all_info.values():
        if info and (info.title or info.plot):
            base_info = info
            break
    
    if not base_info:
        return None
    
    # 简单的数据合并逻辑
    for info in all_info.values():
        if info and info != base_info:
            # 合并缺失的字段
            if not base_info.title and info.title:
                base_info.title = info.title
            if not base_info.plot and info.plot:
                base_info.plot = info.plot
            if not base_info.cover and info.cover:
                base_info.cover = info.cover
            if not base_info.actress and info.actress:
                base_info.actress = info.actress
            if not base_info.genre and info.genre:
                base_info.genre = info.genre
    
    return base_info

def movie_info_to_dict(movie_info):
    """将MovieInfo对象转换为字典"""
    if not movie_info:
        return None
    
    return {
        'code': movie_info.dvdid or movie_info.cid,
        'title': movie_info.title,
        'plot': movie_info.plot,
        'cover': movie_info.cover,
        'big_cover': movie_info.big_cover,
        'genre': movie_info.genre or [],
        'score': movie_info.score,
        'actress': movie_info.actress or [],
        'director': movie_info.director,
        'duration': movie_info.duration,
        'producer': movie_info.producer,
        'publisher': movie_info.publisher,
        'release_date': movie_info.release_date,
        'serial': movie_info.serial,
        'url': movie_info.url,
        'magnet': movie_info.magnet
    }

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'service': 'JavSP Wrapper',
        'timestamp': datetime.now().isoformat(),
        'config_loaded': config_loaded,
        'crawlers_imported': crawlers_imported
    })

@app.route('/api/movie/<code>', methods=['GET'])
def get_movie(code):
    """获取影片信息API"""
    try:
        logger.info(f"收到获取影片信息请求: {code}")
        
        movie_info, error = get_movie_info_by_code(code)
        
        if error:
            return jsonify({
                'success': False,
                'error': error,
                'code': code
            }), 404
        
        if movie_info:
            result = movie_info_to_dict(movie_info)
            return jsonify({
                'success': True,
                'data': result,
                'code': code
            })
        else:
            return jsonify({
                'success': False,
                'error': '未找到影片信息',
                'code': code
            }), 404
            
    except Exception as e:
        logger.error(f"API错误: {e}")
        logger.error(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e),
            'code': code
        }), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """获取服务状态"""
    try:
        cfg = Cfg()
        return jsonify({
            'status': 'running',
            'config_loaded': config_loaded,
            'crawlers_imported': crawlers_imported,
            'available_crawlers': [c.value for c in cfg.crawler.selection.normal] if config_loaded else [],
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print("启动JavSP HTTP包装器服务...")
    print("端口: 3002")
    print("健康检查: http://localhost:3002/health")
    print("API文档: http://localhost:3002/api/movie/{code}")
    
    try:
        # 预初始化
        init_javsp()
        print("JavSP初始化成功")
    except Exception as e:
        print(f"JavSP初始化失败: {e}")
        sys.exit(1)
    
    app.run(host='0.0.0.0', port=3002, debug=False, threaded=True)