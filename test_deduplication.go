package main

import (
	"fmt"
	"log"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/pkg/database"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	err = database.Init(&cfg.Database)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 测试checkCompletedDownloadTask函数
	testCodes := []string{"UBJ-001", "MIDA-182", "NONEXISTENT-001"}

	for _, code := range testCodes {
		fmt.Printf("\n🔍 测试影片代码: %s\n", code)
		
		// 查询是否有已完成的下载任务
		var count int64
		err = database.DB.Model(&model.DownloadTask{}).
			Where("task_name LIKE ? AND status = ? AND processing_status = ? AND (play_url IS NOT NULL AND play_url != '')",
				"%"+code+"%", "completed", "completed").
			Count(&count).Error

		if err != nil {
			fmt.Printf("❌ 查询失败: %v\n", err)
			continue
		}

		if count > 0 {
			fmt.Printf("✅ 发现 %d 个已完成的下载任务，应跳过重复下载\n", count)
			
			// 显示具体的任务信息
			var tasks []model.DownloadTask
			database.DB.Where("task_name LIKE ? AND status = ? AND processing_status = ?",
				"%"+code+"%", "completed", "completed").
				Find(&tasks)
			
			for _, task := range tasks {
				fmt.Printf("   📁 任务: %s\n", task.TaskName)
				fmt.Printf("   🔗 播放链接: %s\n", task.PlayURL)
				fmt.Printf("   📂 保存路径: %s\n", task.SavePath)
			}
		} else {
			fmt.Printf("⚪ 未发现已完成的下载任务，可以下载\n")
		}
	}

	// 统计总体情况
	fmt.Printf("\n📊 系统统计:\n")
	
	var totalCompleted int64
	database.DB.Model(&model.DownloadTask{}).
		Where("status = ? AND processing_status = ? AND (play_url IS NOT NULL AND play_url != '')",
			"completed", "completed").
		Count(&totalCompleted)
	
	fmt.Printf("✅ 已完成且有播放链接的任务总数: %d\n", totalCompleted)
	
	var totalWithFiles int64
	database.DB.Model(&model.DownloadTask{}).
		Where("status = ? AND processing_status = ? AND (play_url IS NOT NULL AND play_url != '') AND (save_path IS NOT NULL AND save_path != '')",
			"completed", "completed").
		Count(&totalWithFiles)
	
	fmt.Printf("📁 有本地文件的已完成任务: %d\n", totalWithFiles)
	fmt.Printf("💾 可清理的文件数量: %d\n", totalWithFiles)
}