# 115网盘客户端使用指南

## 🖥️ **方法1: VNC远程桌面（推荐）**

### **连接VNC桌面**
1. **服务器地址**: `YOUR_SERVER_IP:5901`
2. **密码**: `javapi123`
3. **分辨率**: 1920x1080

### **VNC客户端下载**
- **Windows**: [RealVNC Viewer](https://www.realvnc.com/download/viewer/)
- **macOS**: 系统自带Screen Sharing或RealVNC Viewer
- **Linux**: Remmina, TigerVNC
- **Android/iOS**: VNC Viewer App

### **连接步骤**
1. 启动VNC服务器：`python3 vnc_manager.py start`
2. 打开VNC客户端
3. 输入服务器地址和密码
4. 连接成功后会看到XFCE桌面
5. 在桌面上启动115客户端

### **在VNC桌面中使用115**
```bash
# 在VNC桌面的终端中运行
cd /www/wwwroot/JAVAPI.COM
python3 115_desktop_login.py start
```

## 🌐 **方法2: X11转发（需要本地X服务器）**

### **Linux/macOS用户**
```bash
# SSH连接时启用X11转发
ssh -X root@YOUR_SERVER_IP

# 启动115客户端
cd /www/wwwroot/JAVAPI.COM
python3 115_desktop_login.py start
```

### **Windows用户**
1. 安装Xming或VcXsrv
2. 使用PuTTY连接并启用X11转发
3. 运行115客户端

## 📱 **方法3: 网页API方式（无图形界面）**

### **二维码登录**
```bash
cd /www/wwwroot/JAVAPI.COM
python3 115_simple_login.py login
```

### **用户名密码登录**
```bash
cd /www/wwwroot/JAVAPI.COM
python3 115_web_login.py web
```

## 🛠️ **115客户端功能**

### **主要功能**
- ✅ 文件上传/下载
- ✅ 文件夹同步
- ✅ 大文件传输
- ✅ 断点续传
- ✅ 离线下载
- ✅ 分享链接管理

### **使用技巧**
1. **批量上传**: 直接拖拽文件夹到115客户端
2. **高速下载**: 使用115客户端比网页版更快
3. **自动同步**: 设置本地文件夹与115网盘同步
4. **离线下载**: 添加磁力链接或BT种子

## 🔧 **管理命令**

### **VNC服务器管理**
```bash
# 启动VNC
python3 vnc_manager.py start

# 停止VNC
python3 vnc_manager.py stop

# 查看状态
python3 vnc_manager.py status

# 重启VNC
python3 vnc_manager.py restart
```

### **115客户端管理**
```bash
# 启动115客户端
python3 115_desktop_login.py start

# 停止115客户端
python3 115_desktop_login.py stop

# 查看状态
python3 115_desktop_login.py status

# 重启115客户端
python3 115_desktop_login.py restart
```

## 🚀 **快速开始**

### **第一次使用**
1. 启动VNC服务器
2. 使用VNC客户端连接
3. 在VNC桌面中启动115客户端
4. 登录115账号
5. 开始使用115网盘功能

### **日常使用**
1. 检查VNC状态：`python3 vnc_manager.py status`
2. 如果VNC未运行，启动它：`python3 vnc_manager.py start`
3. 连接VNC桌面
4. 使用115客户端进行文件操作

## 📞 **故障排除**

### **VNC连接问题**
- 检查防火墙是否开放5901端口
- 确认VNC服务器正在运行
- 验证密码是否正确

### **115客户端问题**
- 确认依赖库已安装
- 检查115客户端进程状态
- 重启115客户端服务

### **性能优化**
- 调整VNC分辨率以适应网络带宽
- 使用压缩选项减少网络流量
- 关闭不必要的桌面特效

## 🔐 **安全建议**

1. **修改VNC密码**: 编辑`~/.vnc/passwd`
2. **限制访问IP**: 配置防火墙规则
3. **使用SSH隧道**: 通过SSH加密VNC连接
4. **定期更新**: 保持系统和软件最新

---

**🎯 推荐使用VNC远程桌面方式，这是最稳定和功能完整的使用方法！**