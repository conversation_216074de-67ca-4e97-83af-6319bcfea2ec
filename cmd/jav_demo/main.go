package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/scraper"
	"magnet-downloader/internal/service"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	fmt.Println("=== JAV数据采集演示程序 ===")
	fmt.Println("正在初始化系统...")

	// 初始化数据库
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建Repository
	repo := repository.NewRepository(db)

	// 创建配置
	cfg := createDemoConfig()

	// 创建HTTP客户端
	httpClient := scraper.NewHTTPClient(&scraper.HTTPClientConfig{
		Timeout:   30 * time.Second,
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		RateLimit: 2 * time.Second,
	})

	// 创建爬虫服务
	scraperService := service.NewJAVScraperService(repo, httpClient, &cfg.JAV.Scraping)

	// 创建JAV服务
	javService := service.NewJAVService(repo, &cfg.JAV)

	fmt.Println("✅ 系统初始化完成")
	fmt.Println()

	// 演示数据采集流程
	demonstrateDataCollection(scraperService, javService, repo)
}

func initDatabase() (*gorm.DB, error) {
	// 使用SQLite数据库进行演示
	db, err := gorm.Open(sqlite.Open("jav_demo.db"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	err = db.AutoMigrate(
		&model.JAVMovie{},
		&model.JAVActor{},
		&model.JAVMagnet{},
		&model.JAVGenre{},
	)
	if err != nil {
		return nil, err
	}

	return db, nil
}

func createDemoConfig() *config.Config {
	return &config.Config{
		JAV: config.JAVConfig{
			Enabled: true,
			Scraping: config.JAVScrapingConfig{
				Enabled:         true,
				Timeout:         30,
				RateLimit:       2,
				MaxRetries:      3,
				AutoMerge:       true,
				MinConfidence:   0.7,
				BatchSize:       5,
				ConcurrentLimit: 2,
				UserAgent:       "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				Sources: config.JAVScrapingSourceConfig{
					JavBus: config.JAVSourceConfig{
						Enabled:    true,
						BaseURL:    "https://www.javbus.com",
						Timeout:    30,
						MaxRetries: 3,
						Priority:   9,
						Weight:     0.5,
						RateLimit:  2,
					},
				},
			},
		},
	}
}

func demonstrateDataCollection(scraperService service.JAVScraperService, javService service.JAVService, repo repository.Repository) {
	fmt.Println("🎬 开始演示JAV数据采集流程")
	fmt.Println()

	// 演示影片代码列表（这些是示例代码，实际使用时需要真实的影片代码）
	movieCodes := []string{
		"DEMO-001", "DEMO-002", "DEMO-003", "DEMO-004", "DEMO-005",
		"DEMO-006", "DEMO-007", "DEMO-008", "DEMO-009", "DEMO-010",
		"DEMO-011", "DEMO-012", "DEMO-013", "DEMO-014", "DEMO-015",
		"DEMO-016", "DEMO-017", "DEMO-018", "DEMO-019", "DEMO-020",
	}

	fmt.Printf("📋 准备采集 %d 部影片\n", len(movieCodes))
	fmt.Println()

	// 统计信息
	stats := &CollectionStats{
		Total:     len(movieCodes),
		StartTime: time.Now(),
	}

	// 分批处理影片
	batchSize := 5
	for i := 0; i < len(movieCodes); i += batchSize {
		end := i + batchSize
		if end > len(movieCodes) {
			end = len(movieCodes)
		}

		batch := movieCodes[i:end]
		fmt.Printf("📦 处理批次 %d-%d (%d部影片)\n", i+1, end, len(batch))

		processBatch(batch, scraperService, javService, repo, stats)

		fmt.Printf("✅ 批次 %d-%d 处理完成\n", i+1, end)
		fmt.Println()

		// 批次间休息
		if end < len(movieCodes) {
			fmt.Println("⏳ 休息2秒...")
			time.Sleep(2 * time.Second)
		}
	}

	// 显示最终统计
	showFinalStats(stats, repo)
}

type CollectionStats struct {
	Total       int
	Processed   int
	Success     int
	Failed      int
	NewMovies   int
	NewActors   int
	NewMagnets  int
	StartTime   time.Time
	EndTime     time.Time
}

func processBatch(movieCodes []string, scraperService service.JAVScraperService, javService service.JAVService, repo repository.Repository, stats *CollectionStats) {
	for _, code := range movieCodes {
		fmt.Printf("  🎯 采集影片: %s", code)

		// 检查影片是否已存在
		existingMovie, err := repo.JAVMovie().GetByCode(code)
		if err != nil && err != repository.ErrNotFound {
			fmt.Printf(" ❌ 检查失败: %v\n", err)
			stats.Failed++
			continue
		}

		if existingMovie != nil {
			fmt.Printf(" ⏭️  已存在，跳过\n")
			stats.Processed++
			continue
		}

		// 创建演示数据（实际环境中这里会调用真实的爬虫）
		result := createDemoScrapingResult(code)

		if !result.Success {
			fmt.Printf(" ❌ 采集失败: %s\n", result.Error)
			stats.Failed++
			continue
		}

		// 保存影片数据
		if err := saveMovieData(repo, result.MovieInfo); err != nil {
			fmt.Printf(" ❌ 保存失败: %v\n", err)
			stats.Failed++
			continue
		}

		fmt.Printf(" ✅ 采集成功\n")
		stats.Success++
		stats.NewMovies++
		if result.MovieInfo != nil {
			stats.NewActors += len(result.MovieInfo.Actors)
			stats.NewMagnets += len(result.MovieInfo.Magnets)
		}

		stats.Processed++

		// 影片间休息
		time.Sleep(500 * time.Millisecond)
	}
}

func createDemoScrapingResult(code string) *service.JAVScrapingResult {
	// 创建演示数据
	releaseDate := time.Now().AddDate(0, 0, -30) // 30天前发布

	movieInfo := &service.JAVMovieInfo{
		Code:        code,
		Title:       fmt.Sprintf("演示影片 %s", code),
		TitleEn:     fmt.Sprintf("Demo Movie %s", code),
		Studio:      "演示工作室",
		ReleaseDate: &releaseDate,
		Duration:    120,
		Rating:      7.5 + float64(len(code)%3), // 7.5-9.5之间的评分
		Plot:        fmt.Sprintf("这是影片 %s 的演示简介", code),
		PlotEn:      fmt.Sprintf("This is a demo plot for movie %s", code),
		CoverURL:    fmt.Sprintf("https://example.com/covers/%s.jpg", code),
		PosterURL:   fmt.Sprintf("https://example.com/posters/%s.jpg", code),
		Actors: []service.JAVActorInfo{
			{
				Name:   fmt.Sprintf("演员A-%s", code[len(code)-3:]),
				NameEn: fmt.Sprintf("Actor A-%s", code[len(code)-3:]),
				NameJp: fmt.Sprintf("俳優A-%s", code[len(code)-3:]),
			},
			{
				Name:   fmt.Sprintf("演员B-%s", code[len(code)-3:]),
				NameEn: fmt.Sprintf("Actor B-%s", code[len(code)-3:]),
				NameJp: fmt.Sprintf("俳優B-%s", code[len(code)-3:]),
			},
		},
		Genres: []service.JAVGenreInfo{
			{Name: "演示分类1", NameEn: "Demo Genre 1"},
			{Name: "演示分类2", NameEn: "Demo Genre 2"},
		},
		Magnets: []service.JAVMagnetInfo{
			{
				MagnetURL:        fmt.Sprintf("magnet:?xt=urn:btih:demo%s1080p", code),
				FileName:         fmt.Sprintf("%s.1080p.mp4", code),
				FileSize:         3221225472, // 3GB
				Quality:          "1080p",
				HasSubtitle:      true,
				SubtitleLanguage: "chinese",
				Source:           "demo",
				Seeders:          50,
				Leechers:         10,
			},
			{
				MagnetURL:        fmt.Sprintf("magnet:?xt=urn:btih:demo%s720p", code),
				FileName:         fmt.Sprintf("%s.720p.mp4", code),
				FileSize:         1610612736, // 1.5GB
				Quality:          "720p",
				HasSubtitle:      false,
				SubtitleLanguage: "none",
				Source:           "demo",
				Seeders:          30,
				Leechers:         5,
			},
		},
	}

	return &service.JAVScrapingResult{
		Success:    true,
		MovieInfo:  movieInfo,
		Source:     "demo",
		Duration:   time.Second,
		Confidence: 0.95,
	}
}

func saveMovieData(repo repository.Repository, movieInfo *service.JAVMovieInfo) error {
	if movieInfo == nil {
		return fmt.Errorf("movie info is nil")
	}

	// 创建影片记录
	movie := &model.JAVMovie{
		Code:           movieInfo.Code,
		Title:          movieInfo.Title,
		TitleEn:        movieInfo.TitleEn,
		Studio:         movieInfo.Studio,
		ReleaseDate:    movieInfo.ReleaseDate,
		Duration:       movieInfo.Duration,
		Rating:         movieInfo.Rating,
		Plot:           movieInfo.Plot,
		PlotEn:         movieInfo.PlotEn,
		CoverURL:       movieInfo.CoverURL,
		PosterURL:      movieInfo.PosterURL,
		ScrapingStatus: model.JAVScrapingStatusCompleted,
		ScrapingSource: model.JAVScrapingSourceDemo,
	}

	if err := repo.JAVMovie().Create(movie); err != nil {
		return fmt.Errorf("failed to create movie: %w", err)
	}

	// 保存演员信息
	for _, actorInfo := range movieInfo.Actors {
		// 检查演员是否已存在
		actor, err := repo.JAVActor().GetByName(actorInfo.Name)
		if err != nil && err != repository.ErrNotFound {
			continue
		}

		if actor == nil {
			// 创建新演员
			actor = &model.JAVActor{
				Name:   actorInfo.Name,
				NameEn: actorInfo.NameEn,
				NameJp: actorInfo.NameJp,
			}
			if err := repo.JAVActor().Create(actor); err != nil {
				continue
			}
		}

		// 创建影片-演员关联
		if err := repo.JAVMovie().AddMovieActor(movie.ID, actor.ID); err != nil {
			// 忽略重复关联错误
		}
	}

	// 保存分类信息
	for _, genreInfo := range movieInfo.Genres {
		// 检查分类是否已存在
		genre, err := repo.JAVGenre().GetByName(genreInfo.Name)
		if err != nil && err != repository.ErrNotFound {
			continue
		}

		if genre == nil {
			// 创建新分类
			genre = &model.JAVGenre{
				Name:   genreInfo.Name,
				NameEn: genreInfo.NameEn,
			}
			if err := repo.JAVGenre().Create(genre); err != nil {
				continue
			}
		}

		// 创建影片-分类关联
		if err := repo.JAVMovie().AddMovieGenre(movie.ID, genre.ID); err != nil {
			// 忽略重复关联错误
		}
	}

	// 保存磁力链接
	for _, magnetInfo := range movieInfo.Magnets {
		magnet := &model.JAVMagnet{
			MovieID:          movie.ID,
			MagnetURL:        magnetInfo.MagnetURL,
			FileName:         magnetInfo.FileName,
			FileSize:         magnetInfo.FileSize,
			Quality:          convertQuality(magnetInfo.Quality),
			HasSubtitle:      magnetInfo.HasSubtitle,
			SubtitleLanguage: convertSubtitleLanguage(magnetInfo.SubtitleLanguage),
			Source:           magnetInfo.Source,
			Uploader:         "demo",
			Seeders:          magnetInfo.Seeders,
			Leechers:         magnetInfo.Leechers,
			Score:            calculateMagnetScore(&magnetInfo),
		}

		if err := repo.JAVMagnet().Create(magnet); err != nil {
			continue
		}
	}

	return nil
}

func convertQuality(quality string) model.JAVMagnetQuality {
	switch quality {
	case "4K":
		return model.JAVMagnetQuality4K
	case "1080p":
		return model.JAVMagnetQuality1080p
	case "720p":
		return model.JAVMagnetQuality720p
	default:
		return model.JAVMagnetQualityOther
	}
}

func convertSubtitleLanguage(lang string) model.JAVMagnetSubtitleLanguage {
	switch lang {
	case "chinese":
		return model.JAVMagnetSubtitleLanguageChinese
	case "english":
		return model.JAVMagnetSubtitleLanguageEnglish
	case "japanese":
		return model.JAVMagnetSubtitleLanguageJapanese
	default:
		return model.JAVMagnetSubtitleLanguageNone
	}
}

func calculateMagnetScore(magnetInfo *service.JAVMagnetInfo) float64 {
	score := 50.0

	// 根据清晰度加分
	switch magnetInfo.Quality {
	case "4K":
		score += 30
	case "1080p":
		score += 20
	case "720p":
		score += 10
	}

	// 根据字幕加分
	if magnetInfo.HasSubtitle {
		score += 15
		if magnetInfo.SubtitleLanguage == "chinese" {
			score += 5
		}
	}

	// 根据种子数加分
	if magnetInfo.Seeders > 0 {
		score += float64(magnetInfo.Seeders) * 0.1
		if score > 100 {
			score = 100
		}
	}

	return score
}

func showFinalStats(stats *CollectionStats, repo repository.Repository) {
	stats.EndTime = time.Now()
	duration := stats.EndTime.Sub(stats.StartTime)

	fmt.Println("📊 采集完成统计")
	fmt.Println(strings.Repeat("=", 50))
	fmt.Printf("总影片数: %d\n", stats.Total)
	fmt.Printf("已处理: %d\n", stats.Processed)
	fmt.Printf("成功: %d\n", stats.Success)
	fmt.Printf("失败: %d\n", stats.Failed)
	fmt.Printf("新增影片: %d\n", stats.NewMovies)
	fmt.Printf("新增演员: %d\n", stats.NewActors)
	fmt.Printf("新增磁力: %d\n", stats.NewMagnets)
	fmt.Printf("耗时: %v\n", duration)
	fmt.Printf("成功率: %.1f%%\n", float64(stats.Success)/float64(stats.Processed)*100)
	fmt.Println()

	// 查询数据库统计
	showDatabaseStats(repo)

	fmt.Println("🎉 JAV数据采集演示完成！")
}

func showDatabaseStats(repo repository.Repository) {
	fmt.Println("💾 数据库统计")
	fmt.Println(strings.Repeat("-", 30))

	// 统计影片数量
	movieCount, err := repo.JAVMovie().Count()
	if err == nil {
		fmt.Printf("影片总数: %d\n", movieCount)
	}

	// 统计演员数量
	actorCount, err := repo.JAVActor().Count()
	if err == nil {
		fmt.Printf("演员总数: %d\n", actorCount)
	}

	// 统计分类数量
	genreCount, err := repo.JAVGenre().Count()
	if err == nil {
		fmt.Printf("分类总数: %d\n", genreCount)
	}

	// 统计磁力链接数量
	magnetCount, err := repo.JAVMagnet().Count()
	if err == nil {
		fmt.Printf("磁力总数: %d\n", magnetCount)
	}

	fmt.Println()
}