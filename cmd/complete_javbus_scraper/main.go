package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strings"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/database"
	"magnet-downloader/pkg/javscraper"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/telegram"
)

// JavBusAPI响应结构
type JavBusMoviesResponse struct {
	Movies     []JavBusMovie `json:"movies"`
	Pagination Pagination    `json:"pagination"`
}

type JavBusMovie struct {
	ID       string   `json:"id"`
	Title    string   `json:"title"`
	Date     string   `json:"date"`
	CoverURL string   `json:"img"`
	Tags     []string `json:"tags"`
}

type Pagination struct {
	CurrentPage int   `json:"currentPage"`
	HasNextPage bool  `json:"hasNextPage"`
	NextPage    *int  `json:"nextPage"`
	Pages       []int `json:"pages"`
}

// 完整采集统计
type CompleteScrapeStats struct {
	TotalPages       int
	TotalMovies      int
	ProcessedMovies  int
	SuccessMovies    int
	FailedMovies     int
	NewMovies        int
	UpdatedMovies    int
	DownloadsCreated int
	ImagesDownloaded int
	StartTime        time.Time
	EndTime          time.Time
	Errors           []string
}

func main() {
	fmt.Println("🚀 JavBus完整采集器 (含数据库写入、图片下载、自动下载)")
	fmt.Println("=" + strings.Repeat("=", 70))

	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化数据库
	err = database.Init(&cfg.Database)
	if err != nil {
		log.Fatalf("❌ 数据库连接失败: %v", err)
	}

	// 数据库表已手动创建，跳过AutoMigrate
	fmt.Println("✅ 数据库连接成功")

	// 初始化服务
	repo := repository.NewRepository(database.DB)
	javService := service.NewJAVService(repo)
	aria2Service := service.NewAria2Service(&cfg.Aria2)
	telegramService := service.NewTelegramService(&cfg.Telegram)

	// 初始化JAV采集器配置
	javscraperConfig := &javscraper.Config{
		Enabled:    cfg.JAV.Enabled,
		UserAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		RateLimit:  2 * time.Second,
		ExternalServices: javscraper.ExternalServicesConfig{
			JavBusAPI: javscraper.ExternalServiceConfig{
				Enabled: true,
				BaseURL: "http://localhost:3001",
				Timeout: 30 * time.Second,
			},
			JavSPWrapper: javscraper.ExternalServiceConfig{
				Enabled: true,
				BaseURL: "http://localhost:3002",
				Timeout: 30 * time.Second,
			},
			JavinizerWrapper: javscraper.ExternalServiceConfig{
				Enabled: true,
				BaseURL: "http://localhost:3003",
				Timeout: 30 * time.Second,
			},
		},
		Sources: javscraper.SourcesConfig{
			JavBus: javscraper.JavBusConfig{
				Enabled:   true,
				BaseURL:   "https://www.javbus.com",
				Timeout:   30 * time.Second,
				RateLimit: 2 * time.Second,
				MaxPages:  10,
			},
			JavSP: javscraper.JavSPConfig{
				Enabled:   true,
				Sources:   []string{"javdb", "javlibrary", "avsox"},
				Timeout:   30 * time.Second,
				RateLimit: 3 * time.Second,
			},
			Javinizer: javscraper.JavinizerConfig{
				Enabled:   true,
				Sources:   []string{"javlibrary", "r18", "dmm"},
				Timeout:   30 * time.Second,
				RateLimit: 3 * time.Second,
			},
		},
	}

	javscraperManager, err := javscraper.NewManager(javscraperConfig)
	if err != nil {
		log.Fatalf("❌ 初始化JAV采集器失败: %v", err)
	}

	// 初始化统计
	stats := &CompleteScrapeStats{
		StartTime: time.Now(),
		Errors:    []string{},
	}

	fmt.Println("📋 开始JavBus完整分页采集...")
	fmt.Printf("⏰ 开始时间: %s\n", stats.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Println("🔧 功能: 数据库写入 + 图片下载 + 自动下载触发")
	fmt.Println()

	// 测试aria2连接
	fmt.Println("🔌 测试aria2连接...")
	if err := aria2Service.Connect(); err != nil {
		fmt.Printf("⚠️  aria2连接失败: %v (将跳过自动下载)\n", err)
		aria2Service = nil
	} else {
		fmt.Println("✅ aria2连接成功")
	}

	// 执行完整采集
	err = performCompleteScraping(javscraperManager, javService, repo, aria2Service, stats)
	if err != nil {
		log.Fatalf("❌ 完整采集失败: %v", err)
	}

	// 显示最终统计
	showFinalStats(stats, telegramService)
}

func performCompleteScraping(scraperManager *javscraper.Manager, javService service.JAVService, repo repository.Repository, aria2Service service.Aria2Service, stats *CompleteScrapeStats) error {
	javbusAPIURL := "http://localhost:3001/api/movies"
	currentPage := 1
	maxPages := 99999 // 无限制采集，直到没有更多页面

	fmt.Printf("🔍 开始从第1页采集，最大页数限制: %d\n", maxPages)
	fmt.Println()

	for currentPage <= maxPages {
		fmt.Printf("📄 正在采集第 %d 页...\n", currentPage)

		// 获取当前页的影片列表
		movies, pagination, err := fetchMoviesFromPage(javbusAPIURL, currentPage)
		if err != nil {
			errorMsg := fmt.Sprintf("获取第%d页失败: %v", currentPage, err)
			stats.Errors = append(stats.Errors, errorMsg)
			logger.Errorf(errorMsg)

			if strings.Contains(err.Error(), "404") {
				fmt.Printf("✅ 已到达最后一页 (第%d页)\n", currentPage-1)
				break
			}

			currentPage++
			continue
		}

		stats.TotalPages = currentPage
		stats.TotalMovies += len(movies)

		fmt.Printf("  📊 找到 %d 部影片\n", len(movies))

		// 完整处理当前页的影片
		err = processMoviesPageComplete(scraperManager, javService, repo, aria2Service, movies, currentPage, stats)
		if err != nil {
			errorMsg := fmt.Sprintf("处理第%d页影片失败: %v", currentPage, err)
			stats.Errors = append(stats.Errors, errorMsg)
			logger.Errorf(errorMsg)
		}

		// 智能停止条件检查
		if !pagination.HasNextPage {
			fmt.Printf("✅ 已到达最后一页 (第%d页)\n", currentPage)
			break
		}

		// 如果当前页没有影片，可能已到末尾
		if len(movies) == 0 {
			fmt.Printf("⚠️  第%d页没有找到影片，可能已到达末尾\n", currentPage)
			break
		}

		// 安全检查：如果页数过多，询问是否继续
		if currentPage >= 1000 && currentPage%100 == 0 {
			fmt.Printf("⚠️  已采集%d页，继续采集可能需要很长时间...\n", currentPage)
		}

		// 页面间休息
		fmt.Printf("  ⏳ 休息5秒后继续下一页...\n")
		time.Sleep(5 * time.Second)
		fmt.Println()

		currentPage++
	}

	stats.EndTime = time.Now()
	return nil
}

func fetchMoviesFromPage(baseURL string, page int) ([]JavBusMovie, *Pagination, error) {
	url := fmt.Sprintf("%s?page=%d", baseURL, page)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return nil, nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return nil, nil, fmt.Errorf("页面不存在 (404)")
	}

	if resp.StatusCode != 200 {
		return nil, nil, fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
	}

	var response JavBusMoviesResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, nil, fmt.Errorf("解析JSON响应失败: %w", err)
	}

	return response.Movies, &response.Pagination, nil
}

func processMoviesPageComplete(scraperManager *javscraper.Manager, javService service.JAVService, repo repository.Repository, aria2Service service.Aria2Service, movies []JavBusMovie, page int, stats *CompleteScrapeStats) error {
	if len(movies) == 0 {
		return nil
	}

	// 提取影片番号
	var codes []string
	for _, movie := range movies {
		if movie.ID != "" {
			codes = append(codes, strings.ToUpper(strings.TrimSpace(movie.ID)))
		}
	}

	if len(codes) == 0 {
		return fmt.Errorf("第%d页没有有效的影片番号", page)
	}

	fmt.Printf("  🔄 开始完整采集 %d 个影片番号...\n", len(codes))

	// 分批处理，每批3个（减少批次大小，确保质量）
	batchSize := 3
	for i := 0; i < len(codes); i += batchSize {
		end := i + batchSize
		if end > len(codes) {
			end = len(codes)
		}

		batch := codes[i:end]
		fmt.Printf("    📦 处理批次 %d-%d: %v\n", i+1, end, batch)

		// 完整采集每个影片
		for _, code := range batch {
			err := processSingleMovieComplete(scraperManager, javService, repo, aria2Service, code, stats)
			if err != nil {
				errorMsg := fmt.Sprintf("采集影片%s失败: %v", code, err)
				stats.Errors = append(stats.Errors, errorMsg)
				logger.Errorf(errorMsg)
				stats.FailedMovies++
			} else {
				stats.SuccessMovies++
			}
			stats.ProcessedMovies++

			// 影片间休息，避免过快请求
			time.Sleep(2 * time.Second)
		}

		fmt.Printf("    ✅ 批次完成: 成功%d, 失败%d\n",
			min(len(batch), stats.SuccessMovies),
			min(len(batch), stats.FailedMovies))

		// 批次间休息
		if end < len(codes) {
			time.Sleep(3 * time.Second)
		}
	}

	fmt.Printf("  ✅ 第%d页处理完成\n", page)
	return nil
}

func processSingleMovieComplete(scraperManager *javscraper.Manager, javService service.JAVService, repo repository.Repository, aria2Service service.Aria2Service, code string, stats *CompleteScrapeStats) error {
	fmt.Printf("      🎬 采集影片: %s\n", code)

	// 1. 使用JAV采集器获取完整数据
	result, err := scraperManager.GetMovieByCode(code)
	if err != nil {
		return fmt.Errorf("采集器获取数据失败: %w", err)
	}

	if !result.Success {
		return fmt.Errorf("采集失败: %s", result.Error)
	}

	fmt.Printf("        ✅ 数据采集成功 (来源: %s, 耗时: %v)\n", result.Source, result.Duration)

	// 2. 检查影片是否已存在
	existingMovie, err := javService.GetMovieByCode(code)
	isNewMovie := err != nil // 如果获取失败，说明是新影片

	if isNewMovie {
		stats.NewMovies++
		fmt.Printf("        🆕 新影片，将保存到数据库\n")
	} else {
		stats.UpdatedMovies++
		fmt.Printf("        🔄 更新现有影片 (ID: %d)\n", existingMovie.ID)
	}

	// 3. 保存影片数据到数据库
	err = saveMovieToDatabase(repo, result, stats, existingMovie, isNewMovie)
	if err != nil {
		return fmt.Errorf("保存数据库失败: %w", err)
	}

	// 4. 下载图片（封面、演员头像等）
	err = downloadAllImages(result.MovieInfo, code, stats)
	if err != nil {
		fmt.Printf("        ⚠️  图片下载失败: %v\n", err)
	}

	// 5. 智能下载决策：多重检查避免重复下载
	shouldSkipDownload := false
	skipReason := ""

	// 检查1：是否已有播放链接
	if existingMovie != nil && existingMovie.HasStreamingLinks() {
		shouldSkipDownload = true
		skipReason = "已有播放链接"
		fmt.Printf("        🎯 影片已有播放链接，跳过重复下载\n")
		fmt.Printf("           StreamTape: %v\n", existingMovie.StreamTapeURL != "")
		fmt.Printf("           StreamHG: %v\n", existingMovie.StreamHGURL != "")
	}

	// 检查2：是否已有完成的下载任务
	if !shouldSkipDownload {
		hasCompletedTask, err := checkCompletedDownloadTask(code)
		if err != nil {
			fmt.Printf("        ⚠️  检查下载任务失败: %v\n", err)
		} else if hasCompletedTask {
			shouldSkipDownload = true
			skipReason = "已有完成的下载任务"
			fmt.Printf("        🎯 发现已完成的下载任务，跳过重复下载\n")
		}
	}

	// 检查3：是否已有进行中的aria2任务
	if !shouldSkipDownload && aria2Service != nil {
		hasActiveTask, err := checkActiveAria2Task(aria2Service, code)
		if err != nil {
			fmt.Printf("        ⚠️  检查aria2任务失败: %v\n", err)
		} else if hasActiveTask {
			shouldSkipDownload = true
			skipReason = "已有进行中的下载任务"
			fmt.Printf("        🎯 发现进行中的aria2任务，跳过重复下载\n")
		}
	}

	// 6. 如果需要下载且有磁力链接，创建aria2下载任务
	if !shouldSkipDownload && len(result.MovieInfo.Magnets) > 0 {
		fmt.Printf("        🧲 发现 %d 个磁力链接，智能筛选下载任务\n", len(result.MovieInfo.Magnets))

		// 使用智能磁力选择器（优化版：只选择最佳的一个）
		selectedMagnets := selectBestMagnetsAdvanced(result.MovieInfo.Magnets, code)
		if len(selectedMagnets) > 0 {
			for i, magnet := range selectedMagnets {
				fmt.Printf("        📥 创建下载任务 %d/%d: %s\n", i+1, len(selectedMagnets), magnet.MagnetURL)
				fmt.Printf("           📊 评分: %.1f, 大小: %s, 字幕: %v\n",
					magnet.Score, formatFileSize(magnet.FileSize), magnet.HasSubtitle)

				err = createAria2DownloadTask(aria2Service, magnet.MagnetInfo, code)
				if err != nil {
					fmt.Printf("        ⚠️  aria2下载失败: %v\n", err)
					// 如果aria2失败，保存到文件作为备用
					createDownloadTask(magnet.MagnetInfo, code)
					fmt.Printf("        💾 磁力链接已保存到文件\n")
				} else {
					stats.DownloadsCreated++
					fmt.Printf("        ✅ aria2下载任务创建成功\n")
				}
			}
		}
	} else if shouldSkipDownload {
		fmt.Printf("        ⏭️  跳过下载任务创建 (原因: %s)\n", skipReason)
	}

	fmt.Printf("        ✅ 影片 %s 处理完成\n", code)
	return nil
}

// checkCompletedDownloadTask 检查是否已有完成的下载任务
func checkCompletedDownloadTask(movieCode string) (bool, error) {
	// 这里应该查询数据库检查是否已有完成的下载任务
	// 暂时返回false，实际实现需要数据库查询
	return false, nil
}

// checkActiveAria2Task 检查aria2中是否已有相关的活跃任务
func checkActiveAria2Task(aria2Service service.Aria2Service, movieCode string) (bool, error) {
	if aria2Service == nil {
		return false, nil
	}

	// 获取活跃的下载任务
	activeTasks, err := aria2Service.GetActiveDownloads()
	if err != nil {
		return false, err
	}

	// 检查是否有包含该影片代码的任务
	for _, task := range activeTasks {
		if strings.Contains(task.Dir, movieCode) ||
			strings.Contains(task.InfoHash, movieCode) {
			return true, nil
		}
	}

	return false, nil
}

// selectBestMagnetsAdvanced 智能选择磁力链接（智能双选策略）
// 新策略：
// 情况1：有中文字幕时 - 选择最佳中文字幕版本 + 最高质量无字幕版本（确保不重复）
// 情况2：无中文字幕时 - 选择单一最高质量版本
func selectBestMagnetsAdvanced(magnets []javscraper.MagnetInfo, movieCode string) []*ScoredMagnet {
	if len(magnets) == 0 {
		return nil
	}

	fmt.Printf("        🔍 开始智能筛选磁力链接...\n")

	// 分析所有磁力链接
	var chineseSubtitle []*ScoredMagnet
	var withoutSubtitle []*ScoredMagnet

	for i := range magnets {
		// 创建带评分的磁力链接
		scoredMagnet := &ScoredMagnet{
			MagnetInfo: &magnets[i],
		}

		// 解析字幕信息
		parseSubtitleInfo(scoredMagnet)
		// 解析质量信息
		parseQualityInfo(scoredMagnet)
		// 计算评分
		scoredMagnet.Score = calculateMagnetScore(scoredMagnet)

		// 只关注中文字幕，其他字幕归类为无字幕
		if scoredMagnet.HasSubtitle && scoredMagnet.SubtitleLanguage == "chinese" {
			chineseSubtitle = append(chineseSubtitle, scoredMagnet)
		} else {
			withoutSubtitle = append(withoutSubtitle, scoredMagnet)
		}
	}

	fmt.Printf("        📊 分析结果: %d个中文字幕, %d个无字幕\n", len(chineseSubtitle), len(withoutSubtitle))

	var selectedMagnets []*ScoredMagnet

	// 情况1：存在中文字幕版本时 - 智能双选策略
	if len(chineseSubtitle) > 0 {
		fmt.Printf("        🎯 情况1：发现中文字幕版本，执行智能双选策略\n")

		// 优先级1：选择最佳中文字幕版本
		bestChinese := findBestMagnetByQualityAndScore(chineseSubtitle)
		if bestChinese != nil {
			selectedMagnets = append(selectedMagnets, bestChinese)
			fmt.Printf("        ✅ 选择最佳中文字幕版本: %s (质量: %s, 评分: %.1f)\n",
				bestChinese.FileName, bestChinese.Quality, bestChinese.Score)
		}

		// 优先级2：同时选择最高质量无字幕版本（如果不重复）
		if len(withoutSubtitle) > 0 {
			bestNoSub := findBestMagnetByQualityAndScore(withoutSubtitle)
			if bestNoSub != nil && !isSimilarMagnet(bestChinese, bestNoSub) {
				selectedMagnets = append(selectedMagnets, bestNoSub)
				fmt.Printf("        ➕ 同时选择最高质量无字幕版本: %s (质量: %s, 评分: %.1f)\n",
					bestNoSub.FileName, bestNoSub.Quality, bestNoSub.Score)
			} else if bestNoSub != nil {
				fmt.Printf("        ⏭️  跳过无字幕版本: 与中文字幕版本重复\n")
			}
		}
	} else if len(withoutSubtitle) > 0 {
		// 情况2：不存在中文字幕版本时 - 单选策略
		fmt.Printf("        🎯 情况2：无中文字幕版本，选择单一最高质量版本\n")

		bestNoSub := findBestMagnetByQualityAndScore(withoutSubtitle)
		if bestNoSub != nil {
			selectedMagnets = append(selectedMagnets, bestNoSub)
			fmt.Printf("        ✅ 选择最高质量版本: %s (质量: %s, 评分: %.1f)\n",
				bestNoSub.FileName, bestNoSub.Quality, bestNoSub.Score)
		}
	}

	// 输出最终选择结果
	if len(selectedMagnets) > 0 {
		fmt.Printf("        🎯 智能筛选完成，共选择 %d 个磁力链接（智能双选策略）\n", len(selectedMagnets))
	} else {
		fmt.Printf("        ⚠️  未找到合适的磁力链接\n")
	}

	return selectedMagnets
}

// findBestMagnetByQualityAndScore 按质量优先级和评分选择最佳磁力链接
// 质量优先级：4K > 1080p > 720p > 480p > standard，同等质量下选评分最高的
func findBestMagnetByQualityAndScore(magnets []*ScoredMagnet) *ScoredMagnet {
	if len(magnets) == 0 {
		return nil
	}

	// 质量优先级映射
	qualityPriority := map[string]int{
		"4K":       5,
		"1080p":    4,
		"720p":     3,
		"480p":     2,
		"standard": 1,
		"unknown":  1,
		"":         1,
	}

	best := magnets[0]
	for _, magnet := range magnets[1:] {
		// 获取质量优先级
		currentPriority := qualityPriority[magnet.Quality]
		bestPriority := qualityPriority[best.Quality]

		// 质量更高，或质量相同但评分更高
		if currentPriority > bestPriority ||
			(currentPriority == bestPriority && magnet.Score > best.Score) {
			best = magnet
		}
	}

	return best
}

// shouldSelectBackupVersion 判断是否应该选择无字幕备选版本
// 条件：无字幕版本的质量明显优于字幕版本
func shouldSelectBackupVersion(chineseVersion, noSubVersion *ScoredMagnet) bool {
	if chineseVersion == nil || noSubVersion == nil {
		return false
	}

	// 质量优先级映射
	qualityPriority := map[string]int{
		"4K":       5,
		"1080p":    4,
		"720p":     3,
		"480p":     2,
		"standard": 1,
		"unknown":  1,
		"":         1,
	}

	chinesePriority := qualityPriority[chineseVersion.Quality]
	noSubPriority := qualityPriority[noSubVersion.Quality]

	// 无字幕版本质量至少高2个等级（例如：中文字幕是720p，无字幕是4K）
	if noSubPriority >= chinesePriority+2 {
		return true
	}

	// 或者质量高1个等级且文件大小明显更大（50%以上）
	if noSubPriority == chinesePriority+1 &&
		noSubVersion.FileSize > 0 && chineseVersion.FileSize > 0 {
		sizeRatio := float64(noSubVersion.FileSize) / float64(chineseVersion.FileSize)
		if sizeRatio >= 1.5 {
			return true
		}
	}

	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// selectBestQualityMagnets 选择高质量的磁力链接（质量优先策略）
// 参数：
// - magnets: 候选磁力链接列表
// - alreadySelected: 已选择的磁力链接（用于去重）
// - maxCount: 最大选择数量
func selectBestQualityMagnets(magnets []*ScoredMagnet, alreadySelected []*ScoredMagnet, maxCount int) []*ScoredMagnet {
	if len(magnets) == 0 || maxCount <= 0 {
		return nil
	}

	fmt.Printf("        🔍 分析 %d 个无字幕版本，寻找高质量版本...\n", len(magnets))

	// 按质量分组
	qualityGroups := make(map[string][]*ScoredMagnet)
	for _, magnet := range magnets {
		quality := magnet.Quality
		if quality == "" || quality == "unknown" {
			quality = "standard"
		}
		qualityGroups[quality] = append(qualityGroups[quality], magnet)
	}

	// 质量优先级（从高到低）
	qualityPriority := []string{"4K", "1080p", "720p", "480p", "standard"}

	var selectedMagnets []*ScoredMagnet

	// 按质量优先级选择
	for _, quality := range qualityPriority {
		if len(selectedMagnets) >= maxCount {
			break
		}

		qualityMagnets, exists := qualityGroups[quality]
		if !exists || len(qualityMagnets) == 0 {
			continue
		}

		fmt.Printf("        📊 发现 %d 个 %s 质量版本\n", len(qualityMagnets), quality)

		// 在同质量中选择最佳的（按评分排序）
		sort.Slice(qualityMagnets, func(i, j int) bool {
			return qualityMagnets[i].Score > qualityMagnets[j].Score
		})

		// 选择该质量下最好的版本（如果不与已选版本重复）
		for _, magnet := range qualityMagnets {
			if len(selectedMagnets) >= maxCount {
				break
			}

			// 检查是否与已选择的版本重复
			isDuplicate := false
			for _, selected := range alreadySelected {
				if isSimilarMagnet(selected, magnet) {
					isDuplicate = true
					fmt.Printf("        ⚠️  %s 版本与已选字幕版本相似，跳过\n", quality)
					break
				}
			}

			// 检查是否与当前选择的版本重复
			if !isDuplicate {
				for _, selected := range selectedMagnets {
					if isSimilarMagnet(selected, magnet) {
						isDuplicate = true
						break
					}
				}
			}

			if !isDuplicate {
				selectedMagnets = append(selectedMagnets, magnet)
				fmt.Printf("        ✅ 选择 %s 质量版本: %s (评分: %.1f)\n",
					quality, magnet.FileName, magnet.Score)
				break // 每个质量级别只选一个最佳的
			}
		}
	}

	if len(selectedMagnets) == 0 {
		fmt.Printf("        ⚠️  所有无字幕版本都与已选版本相似，未选择额外版本\n")
	} else {
		fmt.Printf("        🎯 共选择 %d 个高质量无字幕版本\n", len(selectedMagnets))
	}

	return selectedMagnets
}

func showFinalStats(stats *CompleteScrapeStats, telegramService service.TelegramService) {
	stats.EndTime = time.Now()
	duration := stats.EndTime.Sub(stats.StartTime)

	fmt.Println()
	fmt.Println("🎉 JavBus完整分页采集完成！")
	fmt.Println("=" + strings.Repeat("=", 70))
	fmt.Printf("📊 采集统计:\n")
	fmt.Printf("  总页数: %d\n", stats.TotalPages)
	fmt.Printf("  发现影片: %d 部\n", stats.TotalMovies)
	fmt.Printf("  处理影片: %d 部\n", stats.ProcessedMovies)
	fmt.Printf("  成功采集: %d 部\n", stats.SuccessMovies)
	fmt.Printf("  失败采集: %d 部\n", stats.FailedMovies)
	fmt.Printf("  新增影片: %d 部\n", stats.NewMovies)
	fmt.Printf("  更新影片: %d 部\n", stats.UpdatedMovies)
	fmt.Printf("  下载任务: %d 个\n", stats.DownloadsCreated)
	fmt.Printf("  下载图片: %d 张\n", stats.ImagesDownloaded)
	fmt.Printf("  成功率: %.1f%%\n", float64(stats.SuccessMovies)/float64(stats.ProcessedMovies)*100)
	fmt.Printf("  总耗时: %v\n", duration)
	if stats.TotalPages > 0 {
		fmt.Printf("  平均每页: %v\n", duration/time.Duration(stats.TotalPages))
	}
	if stats.ProcessedMovies > 0 {
		fmt.Printf("  平均每部: %v\n", duration/time.Duration(stats.ProcessedMovies))
	}

	if len(stats.Errors) > 0 {
		fmt.Printf("\n⚠️  错误信息 (%d个):\n", len(stats.Errors))
		for i, err := range stats.Errors {
			if i < 10 { // 只显示前10个错误
				fmt.Printf("  %d. %s\n", i+1, err)
			}
		}
		if len(stats.Errors) > 10 {
			fmt.Printf("  ... 还有 %d 个错误\n", len(stats.Errors)-10)
		}
	}

	fmt.Printf("\n⏰ 开始时间: %s\n", stats.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("⏰ 结束时间: %s\n", stats.EndTime.Format("2006-01-02 15:04:05"))
	fmt.Println()
	fmt.Println("🎯 完整采集任务已完成！")
	fmt.Println("💾 数据已保存到数据库")
	fmt.Println("🖼️  图片已下载到本地")
	fmt.Println("📥 下载任务已创建，aria2将自动开始下载")

	// 发送Telegram通知
	if telegramService != nil && telegramService.IsEnabled() {
		fmt.Println("📱 发送Telegram通知...")

		// 计算成功率
		successRate := float64(0)
		if stats.ProcessedMovies > 0 {
			successRate = float64(stats.SuccessMovies) / float64(stats.ProcessedMovies) * 100
		}

		telegramStats := &telegram.ScrapingStats{
			TotalPages:       stats.TotalPages,
			TotalMovies:      stats.TotalMovies,
			ProcessedMovies:  stats.ProcessedMovies,
			SuccessMovies:    stats.SuccessMovies,
			FailedMovies:     stats.FailedMovies,
			NewMovies:        stats.NewMovies,
			UpdatedMovies:    stats.UpdatedMovies,
			DownloadsCreated: stats.DownloadsCreated,
			ImagesDownloaded: stats.ImagesDownloaded,
			StartTime:        stats.StartTime,
			EndTime:          stats.EndTime,
			Duration:         duration,
			SuccessRate:      successRate,
			ErrorCount:       len(stats.Errors),
		}

		err := telegramService.SendScrapingCompleted(telegramStats)
		if err != nil {
			fmt.Printf("⚠️  Telegram通知发送失败: %v\n", err)
		} else {
			fmt.Println("✅ Telegram通知发送成功")
		}
	}
}
