package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/javscraper"
	"magnet-downloader/pkg/logger"
)

// saveMovieToDatabase 保存影片数据到数据库
func saveMovieToDatabase(repo repository.Repository, result *javscraper.ScrapingResult, stats *CompleteScrapeStats, existingMovie *service.JAVMovieResponse, isNewMovie bool) error {
	fmt.Printf("        💾 保存数据到数据库...\n")

	// 由于当前JAVService接口没有直接的Create方法，我们需要通过repository直接操作
	// 这里先实现基本的影片信息保存，后续可以扩展为完整的数据保存

	movieInfo := result.MovieInfo
	if movieInfo == nil {
		return fmt.Errorf("影片信息为空")
	}

	// 转换为数据库模型
	movie := convertScrapingResultToMovie(movieInfo)

	// 这里需要直接访问repository来保存数据
	// 由于当前架构限制，我们先记录成功，实际保存需要重构服务接口
	fmt.Printf("        📝 影片信息: %s - %s\n", movie.Code, movie.Title)
	fmt.Printf("        🎬 工作室: %s\n", movie.Studio)
	fmt.Printf("        📅 发布日期: %v\n", movie.ReleaseDate)
	fmt.Printf("        ⏱️  时长: %d分钟\n", movie.Duration)
	fmt.Printf("        ⭐ 评分: %.1f\n", movie.Rating)

	// 实际的数据库保存逻辑
	var err error
	if isNewMovie {
		// 创建新影片
		err = repo.JAVMovie().Create(movie)
		if err != nil {
			return fmt.Errorf("创建影片失败: %w", err)
		}
	} else {
		// 更新现有影片
		movie.ID = uint(existingMovie.ID)
		err = repo.JAVMovie().Update(movie)
		if err != nil {
			return fmt.Errorf("更新影片失败: %w", err)
		}
	}

	// 保存演员信息
	if len(movieInfo.Actors) > 0 {
		fmt.Printf("        👥 保存 %d 个演员信息...\n", len(movieInfo.Actors))
		for _, actorInfo := range movieInfo.Actors {
			// 检查演员是否已存在
			existingActor, err := repo.JAVActor().GetByName(actorInfo.Name)
			if err != nil && err.Error() != "record not found" {
				fmt.Printf("        ⚠️  查询演员失败: %v\n", err)
				continue
			}

			var actor *model.JAVActor
			if existingActor == nil {
				// 创建新演员
				actor = &model.JAVActor{
					Name:      actorInfo.Name,
					NameEn:    actorInfo.NameEn,
					NameJp:    actorInfo.NameJp,
					AvatarURL: actorInfo.AvatarURL,
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}
				err = repo.JAVActor().Create(actor)
				if err != nil {
					fmt.Printf("        ⚠️  创建演员失败: %v\n", err)
					continue
				}
			} else {
				actor = existingActor
				// 更新演员头像URL（如果有新的头像URL）
				if actorInfo.AvatarURL != "" && actor.AvatarURL != actorInfo.AvatarURL {
					actor.AvatarURL = actorInfo.AvatarURL
					actor.UpdatedAt = time.Now()
					err = repo.JAVActor().Update(actor)
					if err != nil {
						fmt.Printf("        ⚠️  更新演员头像失败: %v\n", err)
					} else {
						fmt.Printf("        ✅ 更新演员头像: %s -> %s\n", actor.Name, actor.AvatarURL)
					}
				}
			}

			// 创建影片-演员关联 (直接执行SQL)
			err = repo.GetDB().Exec("INSERT INTO jav_movie_actors (movie_id, actor_id, created_at) VALUES (?, ?, ?) ON CONFLICT DO NOTHING",
				movie.ID, actor.ID, time.Now()).Error
			if err != nil {
				fmt.Printf("        ⚠️  创建影片-演员关联失败: %v\n", err)
			}
		}
	}

	// 保存分类信息
	if len(movieInfo.Genres) > 0 {
		fmt.Printf("        🏷️  保存 %d 个分类信息...\n", len(movieInfo.Genres))
		for _, genreInfo := range movieInfo.Genres {
			if genreInfo.Name == "" {
				continue
			}

			// 检查分类是否已存在
			existingGenre, err := repo.JAVGenre().GetByName(genreInfo.Name)
			if err != nil && err.Error() != "record not found" {
				fmt.Printf("        ⚠️  查询分类失败: %v\n", err)
				continue
			}

			var genre *model.JAVGenre
			if existingGenre == nil {
				// 创建新分类
				genre = &model.JAVGenre{
					Name:        genreInfo.Name,
					NameEn:      genreInfo.NameEn,
					NameJp:      genreInfo.NameJp,
					Description: genreInfo.Description,
					CreatedAt:   time.Now(),
				}
				err = repo.JAVGenre().Create(genre)
				if err != nil {
					fmt.Printf("        ⚠️  创建分类失败: %v\n", err)
					continue
				}
			} else {
				genre = existingGenre
			}

			// 创建影片-分类关联 (直接执行SQL)
			err = repo.GetDB().Exec("INSERT INTO jav_movie_genres (movie_id, genre_id, created_at) VALUES (?, ?, ?) ON CONFLICT DO NOTHING",
				movie.ID, genre.ID, time.Now()).Error
			if err != nil {
				fmt.Printf("        ⚠️  创建影片-分类关联失败: %v\n", err)
			}
		}
	}

	// 保存关联影片信息
	fmt.Printf("        🔍 检查关联影片数据: %d 个\n", len(movieInfo.SimilarMovies))
	if len(movieInfo.SimilarMovies) > 0 {
		fmt.Printf("        🔗 保存 %d 个关联影片...\n", len(movieInfo.SimilarMovies))
		for _, similarInfo := range movieInfo.SimilarMovies {
			if similarInfo.Code == "" {
				continue
			}

			// 查找或创建相关影片记录
			var similarMovie *model.JAVMovie
			existingSimilar, err := repo.JAVMovie().GetByCode(similarInfo.Code)
			if err != nil {
				// 创建新的相关影片记录（基本信息）
				similarMovie = &model.JAVMovie{
					Code:           similarInfo.Code,
					Title:          similarInfo.Title,
					CoverURL:       similarInfo.CoverURL,
					ScrapingStatus: model.JAVScrapingStatusPending, // 标记为待采集
					ScrapingSource: model.JAVScrapingSourceJavBus,
					CreatedAt:      time.Now(),
					UpdatedAt:      time.Now(),
				}
				err = repo.JAVMovie().Create(similarMovie)
				if err != nil {
					fmt.Printf("        ⚠️  创建相关影片失败: %v\n", err)
					continue
				}
			} else {
				similarMovie = existingSimilar
			}

			// 创建影片关联关系 (直接执行SQL，避免重复)
			err = repo.GetDB().Exec(`
				INSERT INTO jav_similar_movies (movie_id, similar_movie_id, similarity, created_at, updated_at) 
				VALUES (?, ?, ?, ?, ?) 
				ON CONFLICT (movie_id, similar_movie_id) DO NOTHING
			`, movie.ID, similarMovie.ID, 0.8, time.Now(), time.Now()).Error
			if err != nil {
				fmt.Printf("        ⚠️  创建影片关联失败: %v\n", err)
			}
		}
	}

	// 保存磁力链接信息（带重复检查）
	if len(movieInfo.Magnets) > 0 {
		fmt.Printf("        🧲 保存 %d 个磁力链接...\n", len(movieInfo.Magnets))
		savedCount := 0
		duplicateCount := 0

		for _, magnetInfo := range movieInfo.Magnets {
			if magnetInfo.MagnetURL == "" {
				continue
			}

			// 创建带评分的磁力链接进行字幕检测
			scoredMagnet := &ScoredMagnet{
				MagnetInfo: &magnetInfo,
			}

			// 解析字幕信息（使用增强的检测逻辑）
			parseSubtitleInfo(scoredMagnet)
			// 解析质量信息
			parseQualityInfo(scoredMagnet)
			// 计算评分
			scoredMagnet.Score = calculateMagnetScore(scoredMagnet)

			magnet := &model.JAVMagnet{
				MovieID:          movie.ID,
				MagnetURL:        magnetInfo.MagnetURL,
				FileName:         magnetInfo.FileName,
				FileSize:         magnetInfo.FileSize,
				Quality:          convertQuality(scoredMagnet.Quality),
				HasSubtitle:      scoredMagnet.HasSubtitle, // 使用检测后的结果
				SubtitleLanguage: convertSubtitleLanguage(scoredMagnet.SubtitleLanguage),
				Source:           magnetInfo.Source,
				Uploader:         magnetInfo.Uploader,
				UploadDate:       getTimePointer(magnetInfo.UploadDate),
				Seeders:          magnetInfo.Seeders,
				Leechers:         magnetInfo.Leechers,
				Score:            scoredMagnet.Score,
				SizeScore:        calculateSizeScore(magnetInfo.FileSize),
				SubtitleScore:    calculateSubtitleScore(scoredMagnet),
				QualityScore:     calculateQualityScore(scoredMagnet.Quality),
				SourceScore:      calculateSourceScore(magnetInfo.Source),
				IsSelected:       false,
				CreatedAt:        time.Now(),
				UpdatedAt:        time.Now(),
			}

			err = repo.JAVMagnet().Create(magnet)
			if err != nil {
				// 如果是唯一性约束错误，说明是重复数据
				if strings.Contains(err.Error(), "duplicate") ||
					strings.Contains(err.Error(), "unique") ||
					strings.Contains(err.Error(), "UNIQUE constraint failed") {
					duplicateCount++
					fmt.Printf("        ⚠️  磁力链接已存在，跳过: %s\n", magnetInfo.FileName)
				} else {
					fmt.Printf("        ⚠️  保存磁力链接失败: %v\n", err)
				}
				continue
			}
			savedCount++
		}

		fmt.Printf("        ✅ 磁力链接保存完成: 新增%d个, 重复%d个\n", savedCount, duplicateCount)
	}

	fmt.Printf("        ✅ 数据保存成功\n")
	return nil
}

// convertScrapingResultToMovie 转换采集结果为数据库模型
func convertScrapingResultToMovie(movieInfo *javscraper.MovieInfo) *model.JAVMovie {
	movie := &model.JAVMovie{
		Code:           movieInfo.Code,
		Title:          movieInfo.Title,
		TitleEn:        movieInfo.TitleEn,
		Studio:         movieInfo.Studio,
		Series:         movieInfo.Series,
		Director:       movieInfo.Director,
		Duration:       movieInfo.Duration,
		Rating:         movieInfo.Rating,
		Plot:           movieInfo.Plot,
		PlotEn:         movieInfo.PlotEn,
		CoverURL:       movieInfo.CoverURL,
		PosterURL:      movieInfo.PosterURL,
		ScrapingStatus: model.JAVScrapingStatusCompleted,
		ScrapingSource: model.JAVScrapingSourceJavBus,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// 处理发布日期
	if !movieInfo.ReleaseDate.IsZero() {
		movie.ReleaseDate = &movieInfo.ReleaseDate
	}

	return movie
}

// downloadCoverImage 下载封面图片
func downloadCoverImage(coverURL, movieCode string) error {
	// 创建存储目录
	coverDir := "/www/wwwroot/JAVAPI.COM/storage/covers"
	err := os.MkdirAll(coverDir, 0755)
	if err != nil {
		return fmt.Errorf("创建封面目录失败: %w", err)
	}

	// 获取文件扩展名
	ext := filepath.Ext(coverURL)
	if ext == "" {
		ext = ".jpg" // 默认扩展名
	}

	// 构建本地文件路径
	filename := fmt.Sprintf("%s%s", strings.ToLower(movieCode), ext)
	localPath := filepath.Join(coverDir, filename)

	// 检查文件是否已存在
	if _, err := os.Stat(localPath); err == nil {
		return nil // 文件已存在，跳过下载
	}

	// 下载图片（添加请求头避免403错误）
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", coverURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 添加请求头模拟浏览器访问
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Referer", "https://www.javbus.com/")
	req.Header.Set("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("下载请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		return fmt.Errorf("下载失败，状态码: %d", resp.StatusCode)
	}

	// 创建本地文件
	file, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	// 复制数据
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存文件失败: %w", err)
	}

	logger.Infof("封面下载成功: %s -> %s", coverURL, localPath)
	return nil
}

// createDownloadTask 创建aria2下载任务
func createDownloadTask(magnet *javscraper.MagnetInfo, movieCode string) error {
	// 这里需要调用aria2 RPC API创建下载任务
	// 由于当前没有aria2客户端，我们先记录磁力链接

	// 保存磁力链接到文件，供后续处理
	magnetDir := "/www/wwwroot/JAVAPI.COM/storage/magnets"
	err := os.MkdirAll(magnetDir, 0755)
	if err != nil {
		return fmt.Errorf("创建磁力目录失败: %w", err)
	}

	magnetFile := filepath.Join(magnetDir, fmt.Sprintf("%s.magnet", strings.ToLower(movieCode)))
	file, err := os.Create(magnetFile)
	if err != nil {
		return fmt.Errorf("创建磁力文件失败: %w", err)
	}
	defer file.Close()

	// 写入磁力链接信息
	content := fmt.Sprintf("Movie: %s\nMagnet: %s\nSize: %d\nQuality: %s\nDate: %s\nSubtitle: %t\nSeeders: %d\nLeechers: %d\n",
		movieCode,
		magnet.MagnetURL,
		magnet.FileSize,
		magnet.Quality,
		time.Now().Format("2006-01-02 15:04:05"),
		magnet.HasSubtitle,
		magnet.Seeders,
		magnet.Leechers,
	)

	_, err = file.WriteString(content)
	if err != nil {
		return fmt.Errorf("写入磁力文件失败: %w", err)
	}

	logger.Infof("磁力链接保存成功: %s", magnetFile)

	// TODO: 这里应该调用aria2 RPC API
	// aria2Client.AddURI(magnet.MagnetURL, options)

	return nil
}

// convertQuality 转换质量字符串为枚举类型
func convertQuality(quality string) model.JAVMagnetQuality {
	switch strings.ToLower(quality) {
	case "4k", "uhd":
		return model.JAVMagnetQuality4K
	case "1080p", "fhd":
		return model.JAVMagnetQuality1080p
	case "720p", "hd":
		return model.JAVMagnetQuality720p
	case "480p", "sd":
		return model.JAVMagnetQuality480p
	default:
		return model.JAVMagnetQualityUnknown
	}
}

// convertSubtitleLanguage 转换字幕语言字符串为枚举类型
func convertSubtitleLanguage(language string) model.JAVMagnetSubtitleLanguage {
	switch strings.ToLower(language) {
	case "chinese", "中文", "中字":
		return model.JAVMagnetSubtitleLanguageChinese
	case "english", "英文":
		return model.JAVMagnetSubtitleLanguageEnglish
	case "japanese", "日文":
		return model.JAVMagnetSubtitleLanguageJapanese
	default:
		return model.JAVMagnetSubtitleLanguageNone
	}
}

// getTimePointer 将time.Time转换为*time.Time
func getTimePointer(t time.Time) *time.Time {
	if t.IsZero() {
		return nil
	}
	return &t
}
