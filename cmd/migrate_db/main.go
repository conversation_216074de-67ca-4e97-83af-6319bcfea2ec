package main

import (
	"log"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/database"
	"magnet-downloader/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init("info", "json")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化数据库连接
	err = database.Init(&cfg.Database)
	if err != nil {
		log.Fatalf("❌ 数据库连接失败: %v", err)
	}

	// 运行数据库迁移
	err = database.AutoMigrate()
	if err != nil {
		log.Fatalf("❌ 数据库迁移失败: %v", err)
	}

	log.Println("✅ 数据库迁移完成！")
	log.Println("📊 已创建以下表:")
	log.Println("  - users (用户表)")
	log.Println("  - download_tasks (下载任务表)")
	log.Println("  - system_configs (系统配置表)")
	log.Println("  - jav_movies (JAV影片表)")
	log.Println("  - jav_actors (JAV演员表)")
	log.Println("  - jav_genres (JAV分类表)")
	log.Println("  - jav_magnets (JAV磁力链接表)")
	log.Println("  - jav_samples (JAV样品图表)")
	log.Println("  - jav_scrape_tasks (JAV采集任务表)")
	log.Println("  - jav_downloads (JAV下载表)")
}