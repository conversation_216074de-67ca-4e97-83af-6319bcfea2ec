package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/database"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/logger"
)

// JavBusAPI响应结构
type JavBusMoviesResponse struct {
	Movies     []JavBusMovie `json:"movies"`
	Pagination Pagination    `json:"pagination"`
}

type JavBusMovie struct {
	ID       string `json:"id"`
	Title    string `json:"title"`
	Code     string `json:"code"`
	Date     string `json:"date"`
	CoverURL string `json:"coverUrl"`
	URL      string `json:"url"`
}

type Pagination struct {
	CurrentPage int   `json:"currentPage"`
	HasNextPage bool  `json:"hasNextPage"`
	NextPage    *int  `json:"nextPage"`
	Pages       []int `json:"pages"`
}

// 采集统计
type ScrapeStats struct {
	TotalPages      int
	TotalMovies     int
	ProcessedMovies int
	SuccessMovies   int
	FailedMovies    int
	NewMovies       int
	StartTime       time.Time
	EndTime         time.Time
	Errors          []string
}

func main() {
	fmt.Println("🚀 JavBus批量分页采集器")
	fmt.Println("=" + strings.Repeat("=", 50))

	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 初始化数据库
	db, err := database.NewConnection(cfg.Database)
	if err != nil {
		log.Fatalf("❌ 数据库连接失败: %v", err)
	}
	defer db.Close()

	// 初始化仓库和服务
	repo := repository.NewRepository(db)
	javScraperService := service.NewJAVScraperService(repo)

	// 初始化统计
	stats := &ScrapeStats{
		StartTime: time.Now(),
		Errors:    []string{},
	}

	fmt.Println("📋 开始JavBus批量分页采集...")
	fmt.Printf("⏰ 开始时间: %s\n", stats.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Println()

	// 执行批量采集
	err = performBulkScraping(javScraperService, stats)
	if err != nil {
		log.Fatalf("❌ 批量采集失败: %v", err)
	}

	// 显示最终统计
	showFinalStats(stats)
}

func performBulkScraping(scraperService service.JAVScraperService, stats *ScrapeStats) error {
	javbusAPIURL := "http://localhost:3001/api/movies"
	currentPage := 1
	maxPages := 50 // 设置最大页数限制，避免无限循环

	fmt.Printf("🔍 开始从第1页采集，最大页数限制: %d\n", maxPages)
	fmt.Println()

	for currentPage <= maxPages {
		fmt.Printf("📄 正在采集第 %d 页...\n", currentPage)

		// 获取当前页的影片列表
		movies, pagination, err := fetchMoviesFromPage(javbusAPIURL, currentPage)
		if err != nil {
			errorMsg := fmt.Sprintf("获取第%d页失败: %v", currentPage, err)
			stats.Errors = append(stats.Errors, errorMsg)
			logger.Errorf(errorMsg)
			
			// 如果是404错误，说明已经到达最后一页
			if strings.Contains(err.Error(), "404") {
				fmt.Printf("✅ 已到达最后一页 (第%d页)\n", currentPage-1)
				break
			}
			
			// 其他错误，跳过当前页继续
			currentPage++
			continue
		}

		stats.TotalPages = currentPage
		stats.TotalMovies += len(movies)

		fmt.Printf("  📊 找到 %d 部影片\n", len(movies))

		// 处理当前页的影片
		err = processMoviesPage(scraperService, movies, currentPage, stats)
		if err != nil {
			errorMsg := fmt.Sprintf("处理第%d页影片失败: %v", currentPage, err)
			stats.Errors = append(stats.Errors, errorMsg)
			logger.Errorf(errorMsg)
		}

		// 检查是否有下一页
		if !pagination.HasNextPage {
			fmt.Printf("✅ 已到达最后一页 (第%d页)\n", currentPage)
			break
		}

		// 页面间休息，避免对服务器造成压力
		fmt.Printf("  ⏳ 休息3秒后继续下一页...\n")
		time.Sleep(3 * time.Second)
		fmt.Println()

		currentPage++
	}

	stats.EndTime = time.Now()
	return nil
}

func fetchMoviesFromPage(baseURL string, page int) ([]JavBusMovie, *Pagination, error) {
	url := fmt.Sprintf("%s?page=%d", baseURL, page)
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return nil, nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return nil, nil, fmt.Errorf("页面不存在 (404)")
	}

	if resp.StatusCode != 200 {
		return nil, nil, fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
	}

	var response JavBusMoviesResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, nil, fmt.Errorf("解析JSON响应失败: %w", err)
	}

	return response.Movies, &response.Pagination, nil
}

func processMoviesPage(scraperService service.JAVScraperService, movies []JavBusMovie, page int, stats *ScrapeStats) error {
	if len(movies) == 0 {
		return nil
	}

	// 提取影片番号
	var codes []string
	for _, movie := range movies {
		if movie.Code != "" {
			codes = append(codes, strings.ToUpper(strings.TrimSpace(movie.Code)))
		}
	}

	if len(codes) == 0 {
		return fmt.Errorf("第%d页没有有效的影片番号", page)
	}

	fmt.Printf("  🔄 开始采集 %d 个影片番号...\n", len(codes))

	// 分批处理，每批5个
	batchSize := 5
	for i := 0; i < len(codes); i += batchSize {
		end := i + batchSize
		if end > len(codes) {
			end = len(codes)
		}

		batch := codes[i:end]
		fmt.Printf("    📦 处理批次 %d-%d: %v\n", i+1, end, batch)

		// 调用批量采集服务
		result, err := scraperService.BatchScrapeMovies(batch)
		if err != nil {
			errorMsg := fmt.Sprintf("批次%d-%d采集失败: %v", i+1, end, err)
			stats.Errors = append(stats.Errors, errorMsg)
			logger.Errorf(errorMsg)
			stats.FailedMovies += len(batch)
			continue
		}

		// 更新统计
		stats.ProcessedMovies += len(batch)
		stats.SuccessMovies += result.SuccessCount
		stats.FailedMovies += result.FailureCount

		// 统计新影片数量（简化逻辑）
		for _, movieResult := range result.Results {
			if movieResult.Success && movieResult.IsNew {
				stats.NewMovies++
			}
		}

		fmt.Printf("    ✅ 批次完成: 成功%d, 失败%d\n", result.SuccessCount, result.FailureCount)

		// 批次间休息
		if end < len(codes) {
			time.Sleep(1 * time.Second)
		}
	}

	fmt.Printf("  ✅ 第%d页处理完成\n", page)
	return nil
}

func showFinalStats(stats *ScrapeStats) {
	stats.EndTime = time.Now()
	duration := stats.EndTime.Sub(stats.StartTime)

	fmt.Println()
	fmt.Println("🎉 JavBus批量分页采集完成！")
	fmt.Println("=" + strings.Repeat("=", 50))
	fmt.Printf("📊 采集统计:\n")
	fmt.Printf("  总页数: %d\n", stats.TotalPages)
	fmt.Printf("  发现影片: %d 部\n", stats.TotalMovies)
	fmt.Printf("  处理影片: %d 部\n", stats.ProcessedMovies)
	fmt.Printf("  成功采集: %d 部\n", stats.SuccessMovies)
	fmt.Printf("  失败采集: %d 部\n", stats.FailedMovies)
	fmt.Printf("  新增影片: %d 部\n", stats.NewMovies)
	fmt.Printf("  成功率: %.1f%%\n", float64(stats.SuccessMovies)/float64(stats.ProcessedMovies)*100)
	fmt.Printf("  总耗时: %v\n", duration)
	fmt.Printf("  平均每页: %v\n", duration/time.Duration(stats.TotalPages))
	fmt.Printf("  平均每部: %v\n", duration/time.Duration(stats.ProcessedMovies))

	if len(stats.Errors) > 0 {
		fmt.Printf("\n⚠️  错误信息 (%d个):\n", len(stats.Errors))
		for i, err := range stats.Errors {
			if i < 10 { // 只显示前10个错误
				fmt.Printf("  %d. %s\n", i+1, err)
			}
		}
		if len(stats.Errors) > 10 {
			fmt.Printf("  ... 还有 %d 个错误\n", len(stats.Errors)-10)
		}
	}

	fmt.Printf("\n⏰ 开始时间: %s\n", stats.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("⏰ 结束时间: %s\n", stats.EndTime.Format("2006-01-02 15:04:05"))
	fmt.Println()
	fmt.Println("🎯 采集任务已完成，数据已保存到数据库")
}