package main

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"magnet-downloader/pkg/javscraper"
)

// downloadActorAvatar 下载演员头像
func downloadActorAvatar(avatarURL, actorName string) error {
	// 创建存储目录
	actorDir := "/www/wwwroot/JAVAPI.COM/storage/actors"
	err := os.MkdirAll(actorDir, 0755)
	if err != nil {
		return fmt.Errorf("创建演员目录失败: %w", err)
	}

	// 清理演员名称作为文件名
	safeName := strings.ReplaceAll(actorName, " ", "_")
	safeName = strings.ReplaceAll(safeName, "/", "_")
	safeName = strings.ReplaceAll(safeName, "\\", "_")

	// 获取文件扩展名
	ext := filepath.Ext(avatarURL)
	if ext == "" {
		ext = ".jpg"
	}

	// 构建本地文件路径
	filename := fmt.Sprintf("%s%s", safeName, ext)
	localPath := filepath.Join(actorDir, filename)

	// 检查文件是否已存在
	if _, err := os.Stat(localPath); err == nil {
		fmt.Printf("  📁 文件已存在: %s\n", filename)
		return nil // 文件已存在，跳过下载
	}

	// 使用相同的下载逻辑
	return downloadImageWithHeaders(avatarURL, localPath)
}

// downloadImageWithHeaders 使用请求头下载图片的通用函数
func downloadImageWithHeaders(imageURL, localPath string) error {
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", imageURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	
	// 添加请求头模拟浏览器访问
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Referer", "https://www.javbus.com/")
	req.Header.Set("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP状态错误: %d", resp.StatusCode)
	}

	// 创建本地文件
	file, err := os.Create(localPath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	// 复制内容
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return fmt.Errorf("保存文件失败: %w", err)
	}

	fmt.Printf("  💾 文件保存成功: %s\n", filepath.Base(localPath))
	return nil
}

func main() {
	// 初始化爬虫管理器
	config := &javscraper.Config{
		Enabled:    true,
		UserAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		RateLimit:  2 * time.Second,
		Sources: javscraper.SourcesConfig{
			JavBus: javscraper.JavBusConfig{
				Enabled:   true,
				BaseURL:   "https://www.javbus.com",
				Timeout:   30 * time.Second,
				RateLimit: 3 * time.Second,
				MaxPages:  10,
			},
		},
	}
	
	scraperManager, err := javscraper.NewManager(config)
	if err != nil {
		log.Fatalf("初始化爬虫失败: %v", err)
	}

	// 测试单个影片
	movieCode := "HODV-21978"
	fmt.Printf("🎬 测试采集影片: %s\n", movieCode)

	// 1. 数据采集
	result, err := scraperManager.GetMovieByCode(movieCode)
	if err != nil {
		log.Fatalf("采集失败: %v", err)
	}

	fmt.Printf("✅ 数据采集成功 (来源: %s)\n", result.Source)
	fmt.Printf("📝 影片信息: %s - %s\n", result.MovieInfo.Code, result.MovieInfo.Title)
	fmt.Printf("🎬 工作室: %s\n", result.MovieInfo.Studio)
	fmt.Printf("👥 演员数量: %d\n", len(result.MovieInfo.Actors))

	// 打印演员信息
	for i, actor := range result.MovieInfo.Actors {
		fmt.Printf("  演员%d: %s\n", i+1, actor.Name)
		fmt.Printf("    头像URL: %s\n", actor.AvatarURL)
		fmt.Printf("    英文名: %s\n", actor.NameEn)
		fmt.Printf("    日文名: %s\n", actor.NameJp)
	}

	// 2. 测试演员头像下载
	if len(result.MovieInfo.Actors) > 0 {
		fmt.Printf("\n🖼️  开始测试演员头像下载...\n")
		
		// 创建演员目录
		actorDir := "/www/wwwroot/JAVAPI.COM/storage/actors"
		err := os.MkdirAll(actorDir, 0755)
		if err != nil {
			fmt.Printf("❌ 创建演员目录失败: %v\n", err)
		} else {
			fmt.Printf("✅ 演员目录创建成功: %s\n", actorDir)
		}

		// 下载每个演员的头像
		successCount := 0
		for i, actor := range result.MovieInfo.Actors {
			fmt.Printf("\n👤 处理演员%d: %s\n", i+1, actor.Name)
			
			if actor.AvatarURL == "" {
				fmt.Printf("  ⚠️  演员头像URL为空，跳过下载\n")
				continue
			}

			fmt.Printf("  🔗 头像URL: %s\n", actor.AvatarURL)
			
			// 调用下载函数
			err := downloadActorAvatar(actor.AvatarURL, actor.Name)
			if err != nil {
				fmt.Printf("  ❌ 头像下载失败: %v\n", err)
			} else {
				fmt.Printf("  ✅ 头像下载成功\n")
				successCount++
			}
		}

		fmt.Printf("\n📊 下载统计: 成功 %d/%d\n", successCount, len(result.MovieInfo.Actors))
	}

	// 3. 检查下载结果
	fmt.Printf("\n📁 检查演员头像目录:\n")
	files, err := os.ReadDir("/www/wwwroot/JAVAPI.COM/storage/actors")
	if err != nil {
		fmt.Printf("❌ 读取目录失败: %v\n", err)
	} else {
		if len(files) == 0 {
			fmt.Printf("  📂 目录为空\n")
		} else {
			for _, file := range files {
				info, _ := file.Info()
				fmt.Printf("  📄 %s (%.2f KB)\n", file.Name(), float64(info.Size())/1024)
			}
		}
	}

	fmt.Printf("\n🎉 测试完成！\n")
}