#!/usr/bin/env python3
"""
Aria2实时监控告警系统
多维度监控 + 智能告警 + Telegram通知
"""

import os
import json
import requests
import time
import logging
import sqlite3
import psutil
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 配置
ARIA2_RPC_URL = "http://localhost:6800/jsonrpc"
ARIA2_SECRET = "aria2secret"
DOWNLOAD_DIR = "/downloads"

# Telegram配置（使用用户已有的配置）
TELEGRAM_BOT_TOKEN = "**********************************************"
TELEGRAM_USER_ID = "5278020429"

# 监控数据库
MONITOR_DB_PATH = "/var/lib/aria2_monitor.db"

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

@dataclass
class AlertRule:
    name: str
    metric: str
    threshold: float
    level: AlertLevel
    enabled: bool = True
    cooldown_minutes: int = 30

class Aria2Monitor:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.session = requests.Session()
        self.last_alerts = {}  # 告警冷却记录
        
        # 告警规则配置
        self.alert_rules = [
            AlertRule("存储空间警告", "storage_usage_percent", 85.0, AlertLevel.WARNING, True, 30),
            AlertRule("存储空间紧急", "storage_usage_percent", 95.0, AlertLevel.EMERGENCY, True, 10),
            AlertRule("下载速度过低", "avg_download_speed_kbps", 100.0, AlertLevel.WARNING, True, 60),
            AlertRule("任务失败率过高", "task_failure_rate_percent", 30.0, AlertLevel.CRITICAL, True, 45),
            AlertRule("CPU使用率过高", "cpu_usage_percent", 90.0, AlertLevel.WARNING, True, 15),
            AlertRule("内存使用率过高", "memory_usage_percent", 90.0, AlertLevel.WARNING, True, 15),
            AlertRule("活跃任务数异常", "active_tasks_count", 0, AlertLevel.INFO, True, 120),
        ]
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/var/log/aria2_monitor_alert.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_database(self):
        """设置监控数据库"""
        try:
            os.makedirs(os.path.dirname(MONITOR_DB_PATH), exist_ok=True)
            self.conn = sqlite3.connect(MONITOR_DB_PATH, check_same_thread=False)
            
            # 创建监控数据表
            self.conn.execute('''
                CREATE TABLE IF NOT EXISTS monitor_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    metric_name TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    metadata TEXT
                )
            ''')
            
            # 创建告警记录表
            self.conn.execute('''
                CREATE TABLE IF NOT EXISTS alert_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    alert_name TEXT NOT NULL,
                    alert_level TEXT NOT NULL,
                    metric_value REAL NOT NULL,
                    message TEXT,
                    resolved BOOLEAN DEFAULT FALSE
                )
            ''')
            
            self.conn.commit()
            self.logger.info("监控数据库初始化成功")
            
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")

    def call_aria2(self, method: str, params: List[Any] = None) -> Dict:
        """调用aria2 RPC接口"""
        if params is None:
            params = []
        
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": [f"token:{ARIA2_SECRET}"] + params
        }
        
        try:
            response = self.session.post(ARIA2_RPC_URL, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Aria2 RPC调用失败: {e}")
            return {"error": str(e)}

    def collect_storage_metrics(self) -> Dict[str, float]:
        """收集存储指标"""
        try:
            import shutil
            stat = shutil.disk_usage(DOWNLOAD_DIR)
            total_bytes = stat.total
            used_bytes = stat.used
            free_bytes = stat.free
            
            return {
                "storage_total_gb": total_bytes / (1024**3),
                "storage_used_gb": used_bytes / (1024**3),
                "storage_free_gb": free_bytes / (1024**3),
                "storage_usage_percent": (used_bytes / total_bytes) * 100
            }
        except Exception as e:
            self.logger.error(f"收集存储指标失败: {e}")
            return {}

    def collect_aria2_metrics(self) -> Dict[str, float]:
        """收集aria2指标"""
        metrics = {}
        
        try:
            # 获取全局统计
            global_stat = self.call_aria2("aria2.getGlobalStat")
            if "result" in global_stat:
                result = global_stat["result"]
                metrics.update({
                    "download_speed_bps": float(result.get("downloadSpeed", 0)),
                    "upload_speed_bps": float(result.get("uploadSpeed", 0)),
                    "active_tasks_count": float(result.get("numActive", 0)),
                    "waiting_tasks_count": float(result.get("numWaiting", 0)),
                    "stopped_tasks_count": float(result.get("numStopped", 0))
                })
                
                # 转换为更友好的单位
                metrics["download_speed_kbps"] = metrics["download_speed_bps"] / 1024
                metrics["upload_speed_kbps"] = metrics["upload_speed_bps"] / 1024
            
            # 获取活跃任务详情
            active_tasks = self.call_aria2("aria2.tellActive")
            if "result" in active_tasks:
                tasks = active_tasks["result"]
                if tasks:
                    speeds = [float(task.get("downloadSpeed", 0)) for task in tasks]
                    metrics["avg_download_speed_kbps"] = (sum(speeds) / len(speeds)) / 1024 if speeds else 0
                else:
                    metrics["avg_download_speed_kbps"] = 0
            
            # 计算任务失败率
            stopped_tasks = self.call_aria2("aria2.tellStopped", [0, 100])
            if "result" in stopped_tasks:
                stopped = stopped_tasks["result"]
                if stopped:
                    error_count = sum(1 for task in stopped if task.get("status") == "error")
                    metrics["task_failure_rate_percent"] = (error_count / len(stopped)) * 100
                else:
                    metrics["task_failure_rate_percent"] = 0
                    
        except Exception as e:
            self.logger.error(f"收集aria2指标失败: {e}")
            
        return metrics

    def collect_system_metrics(self) -> Dict[str, float]:
        """收集系统指标"""
        try:
            return {
                "cpu_usage_percent": psutil.cpu_percent(interval=1),
                "memory_usage_percent": psutil.virtual_memory().percent,
                "disk_io_read_mb": psutil.disk_io_counters().read_bytes / (1024**2),
                "disk_io_write_mb": psutil.disk_io_counters().write_bytes / (1024**2),
                "network_sent_mb": psutil.net_io_counters().bytes_sent / (1024**2),
                "network_recv_mb": psutil.net_io_counters().bytes_recv / (1024**2)
            }
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return {}
    
    def store_metrics(self, metrics: Dict[str, float]):
        """存储监控指标"""
        try:
            for metric_name, metric_value in metrics.items():
                self.conn.execute('''
                    INSERT INTO monitor_metrics (metric_name, metric_value)
                    VALUES (?, ?)
                ''', (metric_name, metric_value))
            self.conn.commit()
        except Exception as e:
            self.logger.error(f"存储监控指标失败: {e}")

    def send_telegram_alert(self, message: str, level: AlertLevel):
        """发送Telegram告警"""
        try:
            # 根据告警级别添加emoji
            emoji_map = {
                AlertLevel.INFO: "ℹ️",
                AlertLevel.WARNING: "⚠️",
                AlertLevel.CRITICAL: "🚨",
                AlertLevel.EMERGENCY: "🆘"
            }
            
            formatted_message = f"{emoji_map.get(level, '📊')} **Aria2告警**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
            data = {
                "chat_id": TELEGRAM_USER_ID,
                "text": formatted_message,
                "parse_mode": "Markdown"
            }
            
            response = requests.post(url, data=data, timeout=10)
            response.raise_for_status()
            
            self.logger.info(f"Telegram告警发送成功: {level.value}")
            
        except Exception as e:
            self.logger.error(f"发送Telegram告警失败: {e}")

    def check_alert_cooldown(self, alert_name: str, cooldown_minutes: int) -> bool:
        """检查告警冷却时间"""
        if alert_name not in self.last_alerts:
            return True
            
        last_time = self.last_alerts[alert_name]
        cooldown_delta = timedelta(minutes=cooldown_minutes)
        
        return datetime.now() - last_time > cooldown_delta

    def trigger_alert(self, rule: AlertRule, current_value: float, message: str):
        """触发告警"""
        if not self.check_alert_cooldown(rule.name, rule.cooldown_minutes):
            return
            
        # 记录告警时间
        self.last_alerts[rule.name] = datetime.now()
        
        # 存储告警记录
        try:
            self.conn.execute('''
                INSERT INTO alert_history (alert_name, alert_level, metric_value, message)
                VALUES (?, ?, ?, ?)
            ''', (rule.name, rule.level.value, current_value, message))
            self.conn.commit()
        except Exception as e:
            self.logger.error(f"存储告警记录失败: {e}")
        
        # 发送告警通知
        self.send_telegram_alert(message, rule.level)
        self.logger.warning(f"触发告警: {rule.name} - {message}")

    def evaluate_alerts(self, metrics: Dict[str, float]):
        """评估告警规则"""
        for rule in self.alert_rules:
            if not rule.enabled:
                continue
                
            if rule.metric not in metrics:
                continue
                
            current_value = metrics[rule.metric]
            
            # 根据不同指标类型判断告警条件
            should_alert = False
            
            if rule.metric == "storage_usage_percent":
                should_alert = current_value >= rule.threshold
            elif rule.metric == "avg_download_speed_kbps":
                should_alert = current_value <= rule.threshold and current_value > 0
            elif rule.metric == "task_failure_rate_percent":
                should_alert = current_value >= rule.threshold
            elif rule.metric in ["cpu_usage_percent", "memory_usage_percent"]:
                should_alert = current_value >= rule.threshold
            elif rule.metric == "active_tasks_count":
                should_alert = current_value == rule.threshold  # 0个活跃任务时告警
            
            if should_alert:
                message = self.format_alert_message(rule, current_value, metrics)
                self.trigger_alert(rule, current_value, message)

    def format_alert_message(self, rule: AlertRule, current_value: float, metrics: Dict[str, float]) -> str:
        """格式化告警消息"""
        if rule.metric == "storage_usage_percent":
            free_gb = metrics.get("storage_free_gb", 0)
            return f"**存储空间不足**\n使用率: {current_value:.1f}%\n可用空间: {free_gb:.1f} GB\n阈值: {rule.threshold}%"
            
        elif rule.metric == "avg_download_speed_kbps":
            active_count = metrics.get("active_tasks_count", 0)
            return f"**下载速度过低**\n平均速度: {current_value:.1f} KB/s\n活跃任务: {active_count} 个\n阈值: {rule.threshold} KB/s"
            
        elif rule.metric == "task_failure_rate_percent":
            return f"**任务失败率过高**\n失败率: {current_value:.1f}%\n阈值: {rule.threshold}%"
            
        elif rule.metric == "cpu_usage_percent":
            return f"**CPU使用率过高**\n当前使用率: {current_value:.1f}%\n阈值: {rule.threshold}%"
            
        elif rule.metric == "memory_usage_percent":
            return f"**内存使用率过高**\n当前使用率: {current_value:.1f}%\n阈值: {rule.threshold}%"
            
        elif rule.metric == "active_tasks_count":
            waiting_count = metrics.get("waiting_tasks_count", 0)
            return f"**无活跃下载任务**\n活跃任务: {current_value} 个\n等待任务: {waiting_count} 个"
            
        else:
            return f"**{rule.name}**\n当前值: {current_value}\n阈值: {rule.threshold}"

    def collect_all_metrics(self) -> Dict[str, float]:
        """收集所有监控指标"""
        all_metrics = {}
        
        # 收集各类指标
        storage_metrics = self.collect_storage_metrics()
        aria2_metrics = self.collect_aria2_metrics()
        system_metrics = self.collect_system_metrics()
        
        all_metrics.update(storage_metrics)
        all_metrics.update(aria2_metrics)
        all_metrics.update(system_metrics)
        
        return all_metrics

    def generate_status_report(self) -> str:
        """生成状态报告"""
        metrics = self.collect_all_metrics()
        
        report = "📊 **Aria2系统状态报告**\n\n"
        
        # 存储状态
        if "storage_usage_percent" in metrics:
            report += f"💾 **存储状态**\n"
            report += f"使用率: {metrics['storage_usage_percent']:.1f}%\n"
            report += f"可用空间: {metrics.get('storage_free_gb', 0):.1f} GB\n\n"
        
        # 下载状态
        if "active_tasks_count" in metrics:
            report += f"⬇️ **下载状态**\n"
            report += f"活跃任务: {metrics['active_tasks_count']:.0f} 个\n"
            report += f"等待任务: {metrics.get('waiting_tasks_count', 0):.0f} 个\n"
            report += f"下载速度: {metrics.get('download_speed_kbps', 0):.1f} KB/s\n\n"
        
        # 系统状态
        if "cpu_usage_percent" in metrics:
            report += f"🖥️ **系统状态**\n"
            report += f"CPU使用率: {metrics['cpu_usage_percent']:.1f}%\n"
            report += f"内存使用率: {metrics.get('memory_usage_percent', 0):.1f}%\n\n"
        
        return report

    def cleanup_old_data(self, days: int = 7):
        """清理旧的监控数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            # 清理旧的监控指标
            self.conn.execute('''
                DELETE FROM monitor_metrics 
                WHERE timestamp < ?
            ''', (cutoff_date,))
            
            # 清理旧的告警记录
            self.conn.execute('''
                DELETE FROM alert_history 
                WHERE timestamp < ?
            ''', (cutoff_date,))
            
            self.conn.commit()
            self.logger.info(f"清理了 {days} 天前的监控数据")
            
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")

    def run_monitoring_cycle(self):
        """执行一次监控周期"""
        try:
            # 收集指标
            metrics = self.collect_all_metrics()
            
            if not metrics:
                self.logger.warning("未收集到任何监控指标")
                return
            
            # 存储指标
            self.store_metrics(metrics)
            
            # 评估告警
            self.evaluate_alerts(metrics)
            
            # 记录关键指标
            storage_usage = metrics.get("storage_usage_percent", 0)
            active_tasks = metrics.get("active_tasks_count", 0)
            download_speed = metrics.get("download_speed_kbps", 0)
            
            self.logger.info(f"监控周期完成 - 存储: {storage_usage:.1f}%, 任务: {active_tasks:.0f}, 速度: {download_speed:.1f}KB/s")
            
        except Exception as e:
            self.logger.error(f"监控周期执行失败: {e}")

    def run_monitoring(self, interval: int = 60):
        """运行持续监控"""
        self.logger.info("Aria2监控告警系统启动")
        
        # 发送启动通知
        self.send_telegram_alert("🚀 Aria2监控告警系统已启动", AlertLevel.INFO)
        
        while True:
            try:
                self.run_monitoring_cycle()
                
                # 每天凌晨3点清理旧数据
                if datetime.now().hour == 3 and datetime.now().minute < 2:
                    self.cleanup_old_data()
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                self.logger.info("收到停止信号，退出监控")
                self.send_telegram_alert("⏹️ Aria2监控告警系统已停止", AlertLevel.INFO)
                break
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)

if __name__ == "__main__":
    import sys
    
    monitor = Aria2Monitor()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "monitor":
            # 持续监控模式
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 60
            monitor.run_monitoring(interval)
        elif command == "once":
            # 单次监控
            monitor.run_monitoring_cycle()
        elif command == "report":
            # 生成状态报告
            report = monitor.generate_status_report()
            print(report)
            monitor.send_telegram_alert(report, AlertLevel.INFO)
        elif command == "test":
            # 测试告警
            monitor.send_telegram_alert("🧪 这是一条测试告警消息", AlertLevel.INFO)
        elif command == "cleanup":
            # 清理旧数据
            days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
            monitor.cleanup_old_data(days)
        else:
            print("用法:")
            print("  python3 aria2_monitor_alert.py monitor [间隔秒数]  # 持续监控")
            print("  python3 aria2_monitor_alert.py once              # 单次监控")
            print("  python3 aria2_monitor_alert.py report            # 生成状态报告")
            print("  python3 aria2_monitor_alert.py test              # 测试告警")
            print("  python3 aria2_monitor_alert.py cleanup [天数]     # 清理旧数据")
    else:
        # 默认持续监控
        monitor.run_monitoring()