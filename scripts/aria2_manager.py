#!/usr/bin/env python3
"""
Aria2统一管理工具
提供统一的命令行接口管理所有aria2优化组件
"""

import os
import sys
import json
import subprocess
import time
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

# 配置
SCRIPTS_DIR = "/www/wwwroot/JAVAPI.COM/scripts"
LOGS_DIR = "/var/log"
CONFIG_DIR = "/config"

class Aria2Manager:
    def __init__(self):
        self.setup_logging()
        
        # 组件配置
        self.components = {
            "storage-manager": {
                "script": f"{SCRIPTS_DIR}/aria2_storage_manager.py",
                "service": "aria2-storage-manager",
                "description": "智能存储管理器"
            },
            "retry-manager": {
                "script": f"{SCRIPTS_DIR}/aria2_smart_retry.py",
                "service": "aria2-smart-retry",
                "description": "智能重试管理器"
            },
            "monitor-alert": {
                "script": f"{SCRIPTS_DIR}/aria2_monitor_alert.py",
                "service": "aria2-monitor-alert",
                "description": "实时监控告警系统"
            },
            "adaptive-config": {
                "script": f"{SCRIPTS_DIR}/aria2_adaptive_config.py",
                "service": "aria2-adaptive-config",
                "description": "自适应配置优化"
            },
            "cleaner": {
                "script": f"{SCRIPTS_DIR}/aria2_cleaner_optimized.py",
                "service": "aria2-cleaner-optimized",
                "description": "优化清理脚本"
            }
        }
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{LOGS_DIR}/aria2_manager.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def run_command(self, command: List[str], timeout: int = 30) -> tuple:
        """执行系统命令"""
        try:
            result = subprocess.run(
                command, 
                capture_output=True, 
                text=True, 
                timeout=timeout
            )
            return result.returncode, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            return -1, "", "命令执行超时"
        except Exception as e:
            return -1, "", str(e)

    def get_service_status(self, service_name: str) -> Dict[str, Any]:
        """获取systemd服务状态"""
        returncode, stdout, stderr = self.run_command(
            ["systemctl", "is-active", service_name]
        )
        
        active = stdout.strip() == "active"
        
        # 获取详细状态
        returncode2, stdout2, stderr2 = self.run_command(
            ["systemctl", "status", service_name, "--no-pager", "-l"]
        )
        
        return {
            "active": active,
            "status": stdout.strip(),
            "details": stdout2 if returncode2 == 0 else stderr2
        }

    def get_aria2_status(self) -> Dict[str, Any]:
        """获取aria2状态"""
        try:
            import requests
            
            payload = {
                "jsonrpc": "2.0",
                "id": "1",
                "method": "aria2.getGlobalStat",
                "params": ["token:aria2secret"]
            }
            
            response = requests.post(
                "http://localhost:6800/jsonrpc", 
                json=payload, 
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                if "result" in result:
                    return {
                        "connected": True,
                        "stats": result["result"]
                    }
            
            return {"connected": False, "error": "无法连接到aria2"}
            
        except Exception as e:
            return {"connected": False, "error": str(e)}

    def show_status(self):
        """显示系统状态"""
        print("🎛️  Aria2优化系统状态总览")
        print("=" * 50)
        
        # Aria2核心状态
        print("\n📡 Aria2核心服务")
        aria2_status = self.get_aria2_status()
        if aria2_status["connected"]:
            stats = aria2_status["stats"]
            print(f"  状态: ✅ 运行中")
            print(f"  活跃任务: {stats.get('numActive', 0)} 个")
            print(f"  等待任务: {stats.get('numWaiting', 0)} 个")
            print(f"  下载速度: {int(stats.get('downloadSpeed', 0)) / 1024:.1f} KB/s")
        else:
            print(f"  状态: ❌ 连接失败 - {aria2_status.get('error', '未知错误')}")
        
        # 优化组件状态
        print("\n🔧 优化组件状态")
        for comp_name, comp_config in self.components.items():
            service_status = self.get_service_status(comp_config["service"])
            status_icon = "✅" if service_status["active"] else "❌"
            print(f"  {comp_config['description']}: {status_icon} {service_status['status']}")
        
        # 存储状态
        print("\n💾 存储状态")
        try:
            import shutil
            stat = shutil.disk_usage("/downloads")
            total_gb = stat.total / (1024**3)
            used_gb = stat.used / (1024**3)
            free_gb = stat.free / (1024**3)
            usage_percent = (stat.used / stat.total) * 100
            
            print(f"  总空间: {total_gb:.1f} GB")
            print(f"  已使用: {used_gb:.1f} GB ({usage_percent:.1f}%)")
            print(f"  可用空间: {free_gb:.1f} GB")
            
            if usage_percent > 95:
                print("  ⚠️  存储空间严重不足！")
            elif usage_percent > 85:
                print("  ⚠️  存储空间不足")
        except Exception as e:
            print(f"  ❌ 获取存储信息失败: {e}")

    def start_component(self, component_name: str):
        """启动组件"""
        if component_name not in self.components:
            print(f"❌ 未知组件: {component_name}")
            return False
        
        comp_config = self.components[component_name]
        service_name = comp_config["service"]
        
        print(f"🚀 启动 {comp_config['description']}...")
        
        returncode, stdout, stderr = self.run_command(
            ["systemctl", "start", service_name]
        )
        
        if returncode == 0:
            print(f"✅ {comp_config['description']} 启动成功")
            return True
        else:
            print(f"❌ 启动失败: {stderr}")
            return False

    def stop_component(self, component_name: str):
        """停止组件"""
        if component_name not in self.components:
            print(f"❌ 未知组件: {component_name}")
            return False
        
        comp_config = self.components[component_name]
        service_name = comp_config["service"]
        
        print(f"⏹️  停止 {comp_config['description']}...")
        
        returncode, stdout, stderr = self.run_command(
            ["systemctl", "stop", service_name]
        )
        
        if returncode == 0:
            print(f"✅ {comp_config['description']} 停止成功")
            return True
        else:
            print(f"❌ 停止失败: {stderr}")
            return False

    def restart_component(self, component_name: str):
        """重启组件"""
        if component_name not in self.components:
            print(f"❌ 未知组件: {component_name}")
            return False
        
        comp_config = self.components[component_name]
        service_name = comp_config["service"]
        
        print(f"🔄 重启 {comp_config['description']}...")
        
        returncode, stdout, stderr = self.run_command(
            ["systemctl", "restart", service_name]
        )
        
        if returncode == 0:
            print(f"✅ {comp_config['description']} 重启成功")
            return True
        else:
            print(f"❌ 重启失败: {stderr}")
            return False

    def start_all(self):
        """启动所有组件"""
        print("🚀 启动所有Aria2优化组件...")
        
        success_count = 0
        for comp_name in self.components:
            if self.start_component(comp_name):
                success_count += 1
            time.sleep(2)  # 避免同时启动过多服务
        
        print(f"\n✅ 启动完成: {success_count}/{len(self.components)} 个组件成功启动")

    def stop_all(self):
        """停止所有组件"""
        print("⏹️  停止所有Aria2优化组件...")
        
        success_count = 0
        for comp_name in self.components:
            if self.stop_component(comp_name):
                success_count += 1
        
        print(f"\n✅ 停止完成: {success_count}/{len(self.components)} 个组件成功停止")

    def restart_all(self):
        """重启所有组件"""
        print("🔄 重启所有Aria2优化组件...")
        
        success_count = 0
        for comp_name in self.components:
            if self.restart_component(comp_name):
                success_count += 1
            time.sleep(2)
        
        print(f"\n✅ 重启完成: {success_count}/{len(self.components)} 个组件成功重启")

    def backup_config(self):
        """备份配置文件"""
        print("💾 备份Aria2配置...")
        
        try:
            backup_dir = f"/backup/aria2_configs"
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"{backup_dir}/aria2_{timestamp}.conf"
            
            returncode, stdout, stderr = self.run_command([
                "cp", f"{CONFIG_DIR}/aria2.conf", backup_file
            ])
            
            if returncode == 0:
                print(f"✅ 配置已备份到: {backup_file}")
                return backup_file
            else:
                print(f"❌ 备份失败: {stderr}")
                return None
                
        except Exception as e:
            print(f"❌ 备份异常: {e}")
            return None

    def generate_report(self):
        """生成综合报告"""
        print("📊 生成Aria2系统综合报告...")
        
        report_lines = []
        report_lines.append("# Aria2优化系统综合报告")
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 系统状态
        report_lines.append("## 系统状态")
        aria2_status = self.get_aria2_status()
        if aria2_status["connected"]:
            stats = aria2_status["stats"]
            report_lines.append(f"- Aria2状态: 运行中")
            report_lines.append(f"- 活跃任务: {stats.get('numActive', 0)} 个")
            report_lines.append(f"- 等待任务: {stats.get('numWaiting', 0)} 个")
            report_lines.append(f"- 下载速度: {int(stats.get('downloadSpeed', 0)) / 1024:.1f} KB/s")
        else:
            report_lines.append(f"- Aria2状态: 连接失败")
        
        report_lines.append("")
        
        # 组件状态
        report_lines.append("## 优化组件状态")
        for comp_name, comp_config in self.components.items():
            service_status = self.get_service_status(comp_config["service"])
            status = "运行中" if service_status["active"] else "已停止"
            report_lines.append(f"- {comp_config['description']}: {status}")
        
        report_lines.append("")
        
        # 存储状态
        report_lines.append("## 存储状态")
        try:
            import shutil
            stat = shutil.disk_usage("/downloads")
            total_gb = stat.total / (1024**3)
            used_gb = stat.used / (1024**3)
            free_gb = stat.free / (1024**3)
            usage_percent = (stat.used / stat.total) * 100
            
            report_lines.append(f"- 总空间: {total_gb:.1f} GB")
            report_lines.append(f"- 已使用: {used_gb:.1f} GB ({usage_percent:.1f}%)")
            report_lines.append(f"- 可用空间: {free_gb:.1f} GB")
        except Exception as e:
            report_lines.append(f"- 存储信息获取失败: {e}")
        
        # 保存报告
        report_content = "\n".join(report_lines)
        report_file = f"/tmp/aria2_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"✅ 报告已保存到: {report_file}")
            print("\n" + report_content)
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
            print("\n" + report_content)

    def diagnose_system(self):
        """系统诊断"""
        print("🔍 Aria2系统诊断...")
        
        issues = []
        
        # 检查aria2连接
        aria2_status = self.get_aria2_status()
        if not aria2_status["connected"]:
            issues.append("❌ Aria2服务无法连接")
        
        # 检查组件状态
        for comp_name, comp_config in self.components.items():
            service_status = self.get_service_status(comp_config["service"])
            if not service_status["active"]:
                issues.append(f"❌ {comp_config['description']} 未运行")
        
        # 检查存储空间
        try:
            import shutil
            stat = shutil.disk_usage("/downloads")
            usage_percent = (stat.used / stat.total) * 100
            
            if usage_percent > 95:
                issues.append("❌ 存储空间严重不足 (>95%)")
            elif usage_percent > 85:
                issues.append("⚠️  存储空间不足 (>85%)")
        except Exception as e:
            issues.append(f"❌ 无法检查存储状态: {e}")
        
        # 检查配置文件
        if not os.path.exists(f"{CONFIG_DIR}/aria2.conf"):
            issues.append("❌ Aria2配置文件不存在")
        
        # 检查日志文件
        for comp_name, comp_config in self.components.items():
            log_file = f"{LOGS_DIR}/aria2_{comp_name.replace('-', '_')}.log"
            if not os.path.exists(log_file):
                issues.append(f"⚠️  {comp_config['description']} 日志文件不存在")
        
        # 输出诊断结果
        if not issues:
            print("✅ 系统诊断完成，未发现问题")
        else:
            print(f"🔍 发现 {len(issues)} 个问题:")
            for issue in issues:
                print(f"  {issue}")
            
            print("\n💡 建议修复措施:")
            if "❌ Aria2服务无法连接" in str(issues):
                print("  - 检查aria2服务是否运行: docker ps | grep aria2")
                print("  - 检查端口是否开放: netstat -tlnp | grep 6800")
            
            if any("未运行" in issue for issue in issues):
                print("  - 启动所有组件: python3 aria2_manager.py start-all")
            
            if any("存储" in issue for issue in issues):
                print("  - 执行清理: python3 aria2_cleaner_optimized.py all")
                print("  - 紧急清理: python3 aria2_storage_manager.py emergency")

    def show_logs(self, component_name: str = None, lines: int = 50):
        """显示日志"""
        if component_name and component_name not in self.components:
            print(f"❌ 未知组件: {component_name}")
            return
        
        if component_name:
            # 显示特定组件日志
            log_file = f"{LOGS_DIR}/aria2_{component_name.replace('-', '_')}.log"
            print(f"📋 {self.components[component_name]['description']} 最近 {lines} 行日志:")
            
            returncode, stdout, stderr = self.run_command([
                "tail", "-n", str(lines), log_file
            ])
            
            if returncode == 0:
                print(stdout)
            else:
                print(f"❌ 读取日志失败: {stderr}")
        else:
            # 显示所有组件日志
            print(f"📋 所有组件最近 {lines} 行日志:")
            for comp_name, comp_config in self.components.items():
                log_file = f"{LOGS_DIR}/aria2_{comp_name.replace('-', '_')}.log"
                print(f"\n--- {comp_config['description']} ---")
                
                returncode, stdout, stderr = self.run_command([
                    "tail", "-n", str(lines//len(self.components)), log_file
                ])
                
                if returncode == 0:
                    print(stdout)
                else:
                    print(f"❌ 读取日志失败: {stderr}")

    def emergency_cleanup(self):
        """紧急清理"""
        print("🆘 执行紧急清理...")
        
        # 执行存储管理器紧急清理
        print("1. 执行存储紧急清理...")
        returncode, stdout, stderr = self.run_command([
            "python3", f"{SCRIPTS_DIR}/aria2_storage_manager.py", "emergency"
        ])
        
        if returncode == 0:
            print("✅ 存储紧急清理完成")
        else:
            print(f"❌ 存储清理失败: {stderr}")
        
        # 执行优化清理脚本
        print("2. 执行全面清理...")
        returncode, stdout, stderr = self.run_command([
            "python3", f"{SCRIPTS_DIR}/aria2_cleaner_optimized.py", "all"
        ])
        
        if returncode == 0:
            print("✅ 全面清理完成")
        else:
            print(f"❌ 全面清理失败: {stderr}")
        
        # 重新检查存储状态
        print("3. 检查清理效果...")
        try:
            import shutil
            stat = shutil.disk_usage("/downloads")
            usage_percent = (stat.used / stat.total) * 100
            free_gb = stat.free / (1024**3)
            
            print(f"✅ 清理后存储使用率: {usage_percent:.1f}%")
            print(f"✅ 可用空间: {free_gb:.1f} GB")
            
            if usage_percent < 85:
                print("🎉 紧急清理成功，存储空间已恢复正常")
            else:
                print("⚠️  存储空间仍然紧张，建议手动清理更多文件")
                
        except Exception as e:
            print(f"❌ 检查存储状态失败: {e}")

def main():
    manager = Aria2Manager()
    
    if len(sys.argv) < 2:
        print("🎛️  Aria2统一管理工具")
        print("\n用法:")
        print("  python3 aria2_manager.py <命令> [参数]")
        print("\n命令:")
        print("  status                    # 显示系统状态")
        print("  start <组件名>            # 启动指定组件")
        print("  stop <组件名>             # 停止指定组件")
        print("  restart <组件名>          # 重启指定组件")
        print("  start-all                 # 启动所有组件")
        print("  stop-all                  # 停止所有组件")
        print("  restart-all               # 重启所有组件")
        print("  backup                    # 备份配置文件")
        print("  report                    # 生成综合报告")
        print("  diagnose                  # 系统诊断")
        print("  logs [组件名] [行数]       # 显示日志")
        print("  emergency                 # 紧急清理")
        print("\n可用组件:")
        for comp_name, comp_config in manager.components.items():
            print(f"  {comp_name:<20} # {comp_config['description']}")
        return
    
    command = sys.argv[1]
    
    if command == "status":
        manager.show_status()
    elif command == "start":
        if len(sys.argv) < 3:
            print("❌ 请指定要启动的组件名")
        else:
            manager.start_component(sys.argv[2])
    elif command == "stop":
        if len(sys.argv) < 3:
            print("❌ 请指定要停止的组件名")
        else:
            manager.stop_component(sys.argv[2])
    elif command == "restart":
        if len(sys.argv) < 3:
            print("❌ 请指定要重启的组件名")
        else:
            manager.restart_component(sys.argv[2])
    elif command == "start-all":
        manager.start_all()
    elif command == "stop-all":
        manager.stop_all()
    elif command == "restart-all":
        manager.restart_all()
    elif command == "backup":
        manager.backup_config()
    elif command == "report":
        manager.generate_report()
    elif command == "diagnose":
        manager.diagnose_system()
    elif command == "logs":
        component = sys.argv[2] if len(sys.argv) > 2 else None
        lines = int(sys.argv[3]) if len(sys.argv) > 3 else 50
        manager.show_logs(component, lines)
    elif command == "emergency":
        manager.emergency_cleanup()
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == "__main__":
    main()