#!/usr/bin/env python3
"""
Aria2 完整缓存清理工具
清理所有 aria2 的 cookie、记忆、下载记录、历史和 session ID
"""

import os
import sys
import json
import subprocess
import time
from pathlib import Path

class Aria2CacheCleaner:
    def __init__(self):
        self.project_root = Path("/www/wwwroot/JAVAPI.COM")
        self.docker_volume_path = Path("/var/lib/docker/volumes/javapicom_aria2_config/_data")
        self.local_config_path = self.project_root / "config"
        
        # 需要清理的文件列表
        self.cache_files = [
            "aria2.session",      # session文件
            "dht.dat",           # DHT网络缓存
            "dht6.dat",          # IPv6 DHT缓存
            "aria2.log",         # 日志文件
        ]
        
        # 需要清理的目录
        self.cache_dirs = [
            "script",            # 脚本缓存目录
        ]
        
        print("🧹 Aria2 完整缓存清理工具")
        print("=" * 50)
    
    def stop_aria2_container(self):
        """停止 aria2 Docker 容器"""
        print("🛑 停止 aria2 容器...")
        try:
            result = subprocess.run(
                ["docker", "stop", "aria2"],
                capture_output=True,
                text=True,
                timeout=30
            )
            if result.returncode == 0:
                print("✅ aria2 容器已停止")
                return True
            else:
                print(f"⚠️  停止容器失败: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print("⚠️  停止容器超时，强制终止...")
            subprocess.run(["docker", "kill", "aria2"], capture_output=True)
            return True
        except Exception as e:
            print(f"❌ 停止容器出错: {e}")
            return False
    
    def clear_docker_volume_cache(self):
        """清理 Docker 卷中的缓存文件"""
        print("🗑️  清理 Docker 卷缓存...")
        
        if not self.docker_volume_path.exists():
            print(f"⚠️  Docker 卷路径不存在: {self.docker_volume_path}")
            return False
        
        cleared_count = 0
        
        # 清理缓存文件
        for cache_file in self.cache_files:
            file_path = self.docker_volume_path / cache_file
            if file_path.exists():
                try:
                    if cache_file == "aria2.session":
                        # 清空 session 文件而不是删除
                        file_path.write_text("")
                        print(f"  ✅ 已清空: {cache_file}")
                    else:
                        file_path.unlink()
                        print(f"  ✅ 已删除: {cache_file}")
                    cleared_count += 1
                except Exception as e:
                    print(f"  ❌ 清理失败 {cache_file}: {e}")
            else:
                print(f"  ⏭️  文件不存在: {cache_file}")
        
        # 清理缓存目录
        for cache_dir in self.cache_dirs:
            dir_path = self.docker_volume_path / cache_dir
            if dir_path.exists():
                try:
                    import shutil
                    shutil.rmtree(dir_path)
                    print(f"  ✅ 已删除目录: {cache_dir}")
                    cleared_count += 1
                except Exception as e:
                    print(f"  ❌ 清理目录失败 {cache_dir}: {e}")
            else:
                print(f"  ⏭️  目录不存在: {cache_dir}")
        
        return cleared_count > 0
    
    def clear_local_config_cache(self):
        """清理本地配置目录的缓存"""
        print("🗑️  清理本地配置缓存...")
        
        cleared_count = 0
        
        # 清理本地 session 文件
        local_session = self.local_config_path / "aria2.session"
        if local_session.exists():
            try:
                local_session.write_text("")
                print(f"  ✅ 已清空本地 session 文件")
                cleared_count += 1
            except Exception as e:
                print(f"  ❌ 清理本地 session 失败: {e}")
        
        return cleared_count > 0
    
    def clear_aria2_rpc_cache(self):
        """通过 RPC 清理 aria2 运行时缓存"""
        print("🗑️  清理 aria2 RPC 缓存...")
        
        try:
            import requests
            
            # aria2 RPC 配置
            rpc_url = "http://localhost:6800/jsonrpc"
            
            # 检查 aria2 是否运行
            try:
                response = requests.post(rpc_url, json={
                    "jsonrpc": "2.0",
                    "method": "aria2.getVersion",
                    "id": "test"
                }, timeout=5)
                
                if response.status_code != 200:
                    print("  ⏭️  aria2 RPC 服务未运行")
                    return False
                    
            except requests.exceptions.RequestException:
                print("  ⏭️  aria2 RPC 服务未运行")
                return False
            
            # 获取所有活动任务并停止
            response = requests.post(rpc_url, json={
                "jsonrpc": "2.0",
                "method": "aria2.tellActive",
                "id": "active"
            })
            
            if response.status_code == 200:
                active_tasks = response.json().get("result", [])
                for task in active_tasks:
                    gid = task.get("gid")
                    if gid:
                        requests.post(rpc_url, json={
                            "jsonrpc": "2.0",
                            "method": "aria2.forceRemove",
                            "params": [gid],
                            "id": f"remove_{gid}"
                        })
                print(f"  ✅ 已停止 {len(active_tasks)} 个活动任务")
            
            # 清理等待队列
            response = requests.post(rpc_url, json={
                "jsonrpc": "2.0",
                "method": "aria2.tellWaiting",
                "params": [0, 1000],
                "id": "waiting"
            })
            
            if response.status_code == 200:
                waiting_tasks = response.json().get("result", [])
                for task in waiting_tasks:
                    gid = task.get("gid")
                    if gid:
                        requests.post(rpc_url, json={
                            "jsonrpc": "2.0",
                            "method": "aria2.forceRemove",
                            "params": [gid],
                            "id": f"remove_{gid}"
                        })
                print(f"  ✅ 已清理 {len(waiting_tasks)} 个等待任务")
            
            # 清理已停止任务
            response = requests.post(rpc_url, json={
                "jsonrpc": "2.0",
                "method": "aria2.tellStopped",
                "params": [0, 1000],
                "id": "stopped"
            })
            
            if response.status_code == 200:
                stopped_tasks = response.json().get("result", [])
                for task in stopped_tasks:
                    gid = task.get("gid")
                    if gid:
                        requests.post(rpc_url, json={
                            "jsonrpc": "2.0",
                            "method": "aria2.removeDownloadResult",
                            "params": [gid],
                            "id": f"clear_{gid}"
                        })
                print(f"  ✅ 已清理 {len(stopped_tasks)} 个已停止任务记录")
            
            # 强制保存 session（清空状态）
            requests.post(rpc_url, json={
                "jsonrpc": "2.0",
                "method": "aria2.saveSession",
                "id": "save"
            })
            print("  ✅ 已强制保存空 session")
            
            return True
            
        except ImportError:
            print("  ⚠️  requests 模块未安装，跳过 RPC 清理")
            return False
        except Exception as e:
            print(f"  ❌ RPC 清理失败: {e}")
            return False
    
    def clear_system_temp_files(self):
        """清理系统临时文件中的 aria2 相关文件"""
        print("🗑️  清理系统临时文件...")
        
        temp_paths = [
            "/tmp",
            "/var/tmp",
            self.project_root / "logs"
        ]
        
        aria2_patterns = [
            "aria2*",
            "*aria2*",
            "*.aria2",
            "dht*.dat",
            "*.torrent"
        ]
        
        cleared_count = 0
        
        for temp_path in temp_paths:
            if not Path(temp_path).exists():
                continue
                
            try:
                import glob
                for pattern in aria2_patterns:
                    files = glob.glob(str(Path(temp_path) / pattern))
                    for file_path in files:
                        try:
                            file_obj = Path(file_path)
                            if file_obj.is_file():
                                file_obj.unlink()
                                print(f"  ✅ 已删除: {file_path}")
                                cleared_count += 1
                            elif file_obj.is_dir():
                                import shutil
                                shutil.rmtree(file_path)
                                print(f"  ✅ 已删除目录: {file_path}")
                                cleared_count += 1
                        except Exception as e:
                            print(f"  ❌ 删除失败 {file_path}: {e}")
            except Exception as e:
                print(f"  ❌ 清理 {temp_path} 失败: {e}")
        
        return cleared_count > 0
    
    def clear_download_incomplete_files(self):
        """清理下载目录中的不完整文件"""
        print("🗑️  清理下载目录不完整文件...")
        
        download_dir = Path("/downloads")
        if not download_dir.exists():
            download_dir = self.project_root / "downloads"
        
        if not download_dir.exists():
            print("  ⏭️  下载目录不存在")
            return False
        
        cleared_count = 0
        
        # 清理 .aria2 临时文件
        try:
            import glob
            aria2_files = glob.glob(str(download_dir / "**" / "*.aria2"), recursive=True)
            for aria2_file in aria2_files:
                try:
                    Path(aria2_file).unlink()
                    print(f"  ✅ 已删除临时文件: {Path(aria2_file).name}")
                    cleared_count += 1
                except Exception as e:
                    print(f"  ❌ 删除失败 {aria2_file}: {e}")
        except Exception as e:
            print(f"  ❌ 清理临时文件失败: {e}")
        
        return cleared_count > 0
    
    def restart_aria2_container(self):
        """重启 aria2 容器"""
        print("🔄 重启 aria2 容器...")
        try:
            result = subprocess.run(
                ["docker", "start", "aria2"],
                capture_output=True,
                text=True,
                timeout=30
            )
            if result.returncode == 0:
                print("✅ aria2 容器已重启")
                time.sleep(3)  # 等待服务启动
                return True
            else:
                print(f"❌ 重启容器失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 重启容器出错: {e}")
            return False
    
    def run_complete_cleanup(self):
        """执行完整的清理流程"""
        print("🚀 开始完整清理流程...")
        print()
        
        success_count = 0
        total_steps = 6
        
        # 1. 清理 RPC 缓存（在停止容器前）
        if self.clear_aria2_rpc_cache():
            success_count += 1
        
        # 2. 停止容器
        if self.stop_aria2_container():
            success_count += 1
        
        # 等待容器完全停止
        time.sleep(2)
        
        # 3. 清理 Docker 卷缓存
        if self.clear_docker_volume_cache():
            success_count += 1
        
        # 4. 清理本地配置缓存
        if self.clear_local_config_cache():
            success_count += 1
        
        # 5. 清理系统临时文件
        if self.clear_system_temp_files():
            success_count += 1
        
        # 6. 清理下载目录不完整文件
        if self.clear_download_incomplete_files():
            success_count += 1
        
        print()
        print("=" * 50)
        print(f"🎉 清理完成！成功执行 {success_count}/{total_steps} 个步骤")
        
        # 询问是否重启容器
        try:
            restart = input("是否重启 aria2 容器？(y/N): ").strip().lower()
            if restart in ['y', 'yes']:
                if self.restart_aria2_container():
                    print("✅ aria2 服务已重新启动，所有缓存已清理完毕！")
                else:
                    print("⚠️  请手动重启 aria2 容器: docker start aria2")
            else:
                print("ℹ️  请记得手动重启 aria2 容器: docker start aria2")
        except KeyboardInterrupt:
            print("\n⚠️  请记得手动重启 aria2 容器: docker start aria2")

def main():
    """主函数"""
    try:
        cleaner = Aria2CacheCleaner()
        cleaner.run_complete_cleanup()
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 清理过程出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()