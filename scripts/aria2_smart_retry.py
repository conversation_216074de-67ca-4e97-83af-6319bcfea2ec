#!/usr/bin/env python3
"""
Aria2智能重试管理器
实现分层重试策略和失败原因分析
"""

import os
import json
import requests
import time
import logging
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

# 配置
ARIA2_RPC_URL = "http://localhost:6800/jsonrpc"
ARIA2_SECRET = "aria2secret"
RETRY_DB_PATH = "/var/lib/aria2_retry.db"

class FailureType(Enum):
    NETWORK_ERROR = "network_error"
    TRACKER_FAILED = "tracker_failed"
    INVALID_MAGNET = "invalid_magnet"
    STORAGE_FULL = "storage_full"
    TIMEOUT = "timeout"
    UNKNOWN = "unknown"

@dataclass
class RetryTask:
    gid: str
    magnet_uri: str
    failure_type: FailureType
    retry_count: int
    last_retry: datetime
    next_retry: datetime
    max_retries: int = 10

class SmartRetryManager:
    def __init__(self):
        self.setup_logging()
        self.setup_database()
        self.session = requests.Session()
        
        # 重试策略配置
        self.retry_delays = {
            FailureType.NETWORK_ERROR: [60, 300, 900],  # 1分钟, 5分钟, 15分钟
            FailureType.TRACKER_FAILED: [600, 1800, 3600],  # 10分钟, 30分钟, 1小时
            FailureType.TIMEOUT: [300, 1200, 3600],  # 5分钟, 20分钟, 1小时
            FailureType.STORAGE_FULL: [1800, 3600, 7200],  # 30分钟, 1小时, 2小时
            FailureType.INVALID_MAGNET: [86400],  # 24小时后重试一次
            FailureType.UNKNOWN: [300, 1800, 7200]  # 5分钟, 30分钟, 2小时
        }
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/var/log/aria2_smart_retry.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_database(self):
        """设置数据库"""
        try:
            os.makedirs(os.path.dirname(RETRY_DB_PATH), exist_ok=True)
            self.conn = sqlite3.connect(RETRY_DB_PATH)
            self.conn.execute('''
                CREATE TABLE IF NOT EXISTS retry_tasks (
                    gid TEXT PRIMARY KEY,
                    magnet_uri TEXT NOT NULL,
                    failure_type TEXT NOT NULL,
                    retry_count INTEGER DEFAULT 0,
                    last_retry TIMESTAMP,
                    next_retry TIMESTAMP,
                    max_retries INTEGER DEFAULT 10,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            self.conn.commit()
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")

    def call_aria2(self, method: str, params: List[Any] = None) -> Dict:
        """调用aria2 RPC接口"""
        if params is None:
            params = []
        
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": [f"token:{ARIA2_SECRET}"] + params
        }
        
        try:
            response = self.session.post(ARIA2_RPC_URL, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Aria2 RPC调用失败: {e}")
            return {"error": str(e)}

    def analyze_failure_type(self, task_info: Dict) -> FailureType:
        """分析失败类型"""
        error_code = task_info.get("errorCode", "")
        error_message = task_info.get("errorMessage", "").lower()
        
        # 根据错误代码和消息判断失败类型
        if error_code == "7":  # 磁盘空间不足
            return FailureType.STORAGE_FULL
        elif error_code in ["1", "2", "3"]:  # 网络相关错误
            return FailureType.NETWORK_ERROR
        elif "timeout" in error_message or "timed out" in error_message:
            return FailureType.TIMEOUT
        elif "tracker" in error_message or "announce" in error_message:
            return FailureType.TRACKER_FAILED
        elif "magnet" in error_message or "invalid" in error_message:
            return FailureType.INVALID_MAGNET
        else:
            return FailureType.UNKNOWN    def calculate_next_retry(self, failure_type: FailureType, retry_count: int) -> datetime:
        """计算下次重试时间"""
        delays = self.retry_delays.get(failure_type, [300, 1800, 7200])
        
        if retry_count < len(delays):
            delay_seconds = delays[retry_count]
        else:
            # 使用最后一个延迟时间，并逐渐增加
            delay_seconds = delays[-1] * (2 ** (retry_count - len(delays) + 1))
            delay_seconds = min(delay_seconds, 86400)  # 最大24小时
        
        # 避开网络高峰期 (20:00-23:00)
        next_retry = datetime.now() + timedelta(seconds=delay_seconds)
        hour = next_retry.hour
        
        if 20 <= hour <= 23:
            # 推迟到23:30之后
            next_retry = next_retry.replace(hour=23, minute=30, second=0) + timedelta(days=1 if hour >= 23 else 0)
        
        return next_retry

    def add_retry_task(self, gid: str, magnet_uri: str, task_info: Dict) -> bool:
        """添加重试任务"""
        try:
            failure_type = self.analyze_failure_type(task_info)
            next_retry = self.calculate_next_retry(failure_type, 0)
            
            self.conn.execute('''
                INSERT OR REPLACE INTO retry_tasks 
                (gid, magnet_uri, failure_type, retry_count, last_retry, next_retry, max_retries, updated_at)
                VALUES (?, ?, ?, 0, ?, ?, 10, CURRENT_TIMESTAMP)
            ''', (gid, magnet_uri, failure_type.value, datetime.now(), next_retry))
            
            self.conn.commit()
            self.logger.info(f"添加重试任务: {gid}, 失败类型: {failure_type.value}, 下次重试: {next_retry}")
            return True
            
        except Exception as e:
            self.logger.error(f"添加重试任务失败: {e}")
            return False

    def get_pending_retries(self) -> List[RetryTask]:
        """获取待重试的任务"""
        try:
            cursor = self.conn.execute('''
                SELECT gid, magnet_uri, failure_type, retry_count, last_retry, next_retry, max_retries
                FROM retry_tasks 
                WHERE next_retry <= ? AND retry_count < max_retries
                ORDER BY next_retry ASC
            ''', (datetime.now(),))
            
            tasks = []
            for row in cursor.fetchall():
                tasks.append(RetryTask(
                    gid=row[0],
                    magnet_uri=row[1],
                    failure_type=FailureType(row[2]),
                    retry_count=row[3],
                    last_retry=datetime.fromisoformat(row[4]) if row[4] else None,
                    next_retry=datetime.fromisoformat(row[5]),
                    max_retries=row[6]
                ))
            
            return tasks
            
        except Exception as e:
            self.logger.error(f"获取待重试任务失败: {e}")
            return []

    def enhance_magnet_uri(self, magnet_uri: str, failure_type: FailureType) -> str:
        """增强磁力链接"""
        if failure_type == FailureType.TRACKER_FAILED:
            # 添加更多tracker
            additional_trackers = [
                "udp://tracker.openbittorrent.com:80/announce",
                "udp://tracker.opentrackr.org:1337/announce",
                "udp://9.rarbg.to:2710/announce",
                "udp://exodus.desync.com:6969/announce",
                "udp://tracker.torrent.eu.org:451/announce"
            ]
            
            for tracker in additional_trackers:
                if tracker not in magnet_uri:
                    magnet_uri += f"&tr={tracker}"
        
        return magnet_uri

    def retry_task(self, retry_task: RetryTask) -> bool:
        """重试任务"""
        try:
            # 增强磁力链接
            enhanced_uri = self.enhance_magnet_uri(retry_task.magnet_uri, retry_task.failure_type)
            
            # 添加下载任务
            options = {
                "max-connection-per-server": "8",
                "split": "8",
                "min-split-size": "32M",
                "continue": "true"
            }
            
            # 根据失败类型调整选项
            if retry_task.failure_type == FailureType.TIMEOUT:
                options.update({
                    "timeout": "120",
                    "connect-timeout": "30"
                })
            elif retry_task.failure_type == FailureType.NETWORK_ERROR:
                options.update({
                    "max-tries": "8",
                    "retry-wait": "15"
                })
            
            result = self.call_aria2("aria2.addUri", [[enhanced_uri], options])
            
            if "result" in result:
                new_gid = result["result"]
                
                # 更新重试记录
                new_retry_count = retry_task.retry_count + 1
                next_retry = self.calculate_next_retry(retry_task.failure_type, new_retry_count)
                
                self.conn.execute('''
                    UPDATE retry_tasks 
                    SET gid = ?, retry_count = ?, last_retry = ?, next_retry = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE gid = ?
                ''', (new_gid, new_retry_count, datetime.now(), next_retry, retry_task.gid))
                
                self.conn.commit()
                
                self.logger.info(f"重试任务成功: {retry_task.gid} -> {new_gid}, 重试次数: {new_retry_count}")
                return True
            else:
                self.logger.error(f"重试任务失败: {retry_task.gid}, 错误: {result.get('error', 'Unknown')}")
                return False
                
        except Exception as e:
            self.logger.error(f"重试任务异常: {retry_task.gid} - {e}")
            return False    def scan_failed_tasks(self) -> List[Dict]:
        """扫描失败的任务"""
        failed_tasks = []
        
        # 获取已停止的任务
        result = self.call_aria2("aria2.tellStopped", [0, 100])
        if "result" in result:
            for task in result["result"]:
                if task.get("status") == "error":
                    # 检查是否已经在重试队列中
                    gid = task.get("gid")
                    cursor = self.conn.execute('SELECT gid FROM retry_tasks WHERE gid = ?', (gid,))
                    if not cursor.fetchone():
                        failed_tasks.append(task)
        
        return failed_tasks

    def process_failed_tasks(self) -> int:
        """处理失败的任务"""
        failed_tasks = self.scan_failed_tasks()
        processed_count = 0
        
        for task in failed_tasks:
            gid = task.get("gid")
            files = task.get("files", [])
            
            # 尝试从文件信息中提取磁力链接
            magnet_uri = None
            for file_info in files:
                uris = file_info.get("uris", [])
                for uri_info in uris:
                    uri = uri_info.get("uri", "")
                    if uri.startswith("magnet:"):
                        magnet_uri = uri
                        break
                if magnet_uri:
                    break
            
            if magnet_uri and self.add_retry_task(gid, magnet_uri, task):
                processed_count += 1
                
                # 从aria2中移除失败的任务
                self.call_aria2("aria2.removeDownloadResult", [gid])
        
        return processed_count

    def process_retries(self) -> int:
        """处理重试任务"""
        pending_retries = self.get_pending_retries()
        success_count = 0
        
        for retry_task in pending_retries:
            if self.retry_task(retry_task):
                success_count += 1
            
            # 限制并发重试数量
            if success_count >= 3:
                break
                
        return success_count

    def cleanup_old_tasks(self, days: int = 7) -> int:
        """清理旧的重试记录"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            cursor = self.conn.execute('''
                DELETE FROM retry_tasks 
                WHERE (retry_count >= max_retries OR updated_at < ?)
            ''', (cutoff_date,))
            
            deleted_count = cursor.rowcount
            self.conn.commit()
            
            if deleted_count > 0:
                self.logger.info(f"清理了 {deleted_count} 个旧的重试记录")
            
            return deleted_count
            
        except Exception as e:
            self.logger.error(f"清理旧任务失败: {e}")
            return 0

    def get_retry_statistics(self) -> Dict[str, Any]:
        """获取重试统计信息"""
        try:
            stats = {}
            
            # 总重试任务数
            cursor = self.conn.execute('SELECT COUNT(*) FROM retry_tasks')
            stats['total_tasks'] = cursor.fetchone()[0]
            
            # 按失败类型统计
            cursor = self.conn.execute('''
                SELECT failure_type, COUNT(*) 
                FROM retry_tasks 
                GROUP BY failure_type
            ''')
            stats['by_failure_type'] = dict(cursor.fetchall())
            
            # 待重试任务数
            cursor = self.conn.execute('''
                SELECT COUNT(*) FROM retry_tasks 
                WHERE next_retry <= ? AND retry_count < max_retries
            ''', (datetime.now(),))
            stats['pending_retries'] = cursor.fetchone()[0]
            
            # 已达到最大重试次数的任务
            cursor = self.conn.execute('''
                SELECT COUNT(*) FROM retry_tasks 
                WHERE retry_count >= max_retries
            ''')
            stats['max_retries_reached'] = cursor.fetchone()[0]
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {}

    def run_monitoring(self, interval: int = 300):
        """运行监控循环"""
        self.logger.info("Aria2智能重试管理器启动")
        
        while True:
            try:
                # 处理失败的任务
                failed_count = self.process_failed_tasks()
                if failed_count > 0:
                    self.logger.info(f"处理了 {failed_count} 个失败任务")
                
                # 处理重试任务
                retry_count = self.process_retries()
                if retry_count > 0:
                    self.logger.info(f"重试了 {retry_count} 个任务")
                
                # 定期清理
                if datetime.now().hour == 3:  # 凌晨3点清理
                    self.cleanup_old_tasks()
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                self.logger.info("收到停止信号，退出监控")
                break
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)

if __name__ == "__main__":
    import sys
    
    manager = SmartRetryManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "monitor":
            # 持续监控模式
            manager.run_monitoring(interval=300)  # 5分钟检查一次
        elif command == "process":
            # 单次处理
            failed_count = manager.process_failed_tasks()
            retry_count = manager.process_retries()
            print(f"处理失败任务: {failed_count}, 重试任务: {retry_count}")
        elif command == "stats":
            # 显示统计信息
            stats = manager.get_retry_statistics()
            print("重试统计信息:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
        elif command == "cleanup":
            # 清理旧记录
            deleted_count = manager.cleanup_old_tasks()
            print(f"清理了 {deleted_count} 个旧记录")
        else:
            print("用法:")
            print("  python3 aria2_smart_retry.py monitor   # 持续监控模式")
            print("  python3 aria2_smart_retry.py process   # 单次处理")
            print("  python3 aria2_smart_retry.py stats     # 显示统计信息")
            print("  python3 aria2_smart_retry.py cleanup   # 清理旧记录")
    else:
        # 默认单次处理
        failed_count = manager.process_failed_tasks()
        retry_count = manager.process_retries()
        print(f"处理失败任务: {failed_count}, 重试任务: {retry_count}")