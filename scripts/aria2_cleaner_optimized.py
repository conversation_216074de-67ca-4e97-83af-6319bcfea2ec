#!/usr/bin/env python3
"""
Aria2清理脚本优化版
与新的智能存储管理器协同工作，专注于数据库集成和特殊清理任务
"""

import json
import requests
import time
import logging
import os
import shutil
import psycopg2
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

# 配置
ARIA2_RPC_URL = "http://localhost:6800/jsonrpc"
ARIA2_SECRET = "aria2secret"
DOWNLOAD_DIR = "/www/wwwroot/JAVAPI.COM/downloads"

# 数据库配置
DB_CONFIG = {
    "host": "localhost",
    "port": 5432,
    "user": "postgres",
    "password": "postgres123",
    "database": "magnet_downloader"
}

# 优化后的配置 - 避免与新存储管理器冲突
MIN_DIR_SIZE_TO_KEEP = 100 * 1024 * 1024  # 100MB以下的目录会被删除
ORPHAN_FILE_MIN_SIZE = 500 * 1024 * 1024  # 500MB以上的孤儿文件才清理
ORPHAN_FILE_MIN_AGE_HOURS = 6  # 6小时以上的孤儿文件才清理

class Aria2CleanerOptimized:
    def __init__(self):
        self.session = requests.Session()
        self.db_conn = None
        
        # 设置日志 - 使用不同的日志文件避免冲突
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/var/log/aria2_cleaner_optimized.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库连接
        self.init_database_connection()

    def call_aria2(self, method: str, params: List[Any] = None) -> Dict:
        """调用aria2 RPC接口"""
        if params is None:
            params = []
        
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": [f"token:{ARIA2_SECRET}"] + params
        }
        
        try:
            response = self.session.post(ARIA2_RPC_URL, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Aria2 RPC调用失败: {e}")
            return {"error": str(e)}

    def init_database_connection(self):
        """初始化数据库连接"""
        try:
            self.db_conn = psycopg2.connect(**DB_CONFIG)
            self.logger.info("数据库连接成功")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            self.db_conn = None

    def get_uploaded_tasks(self) -> List[Dict]:
        """获取已上传的任务（有播放URL的任务）"""
        if not self.db_conn:
            return []
        
        try:
            cursor = self.db_conn.cursor()
            query = """
                SELECT id, task_name, save_path, play_url, stream_tape_url, aria2_g_id
                FROM download_tasks 
                WHERE (play_url IS NOT NULL AND play_url != '') 
                   OR (stream_tape_url IS NOT NULL AND stream_tape_url != '')
                ORDER BY completed_at DESC
            """
            cursor.execute(query)
            results = cursor.fetchall()
            
            uploaded_tasks = []
            for row in results:
                task_id, task_name, save_path, play_url, stream_tape_url, aria2_g_id = row
                uploaded_tasks.append({
                    "id": task_id,
                    "name": task_name,
                    "save_path": save_path,
                    "play_url": play_url,
                    "stream_tape_url": stream_tape_url,
                    "aria2_gid": aria2_g_id
                })
            
            cursor.close()
            self.logger.info(f"查询到 {len(uploaded_tasks)} 个已上传的任务")
            return uploaded_tasks
            
        except Exception as e:
            self.logger.error(f"查询已上传任务失败: {e}")
            return []

    def get_database_file_paths(self) -> List[str]:
        """获取数据库中记录的所有文件路径"""
        if not self.db_conn:
            return []
        
        try:
            cursor = self.db_conn.cursor()
            cursor.execute("SELECT DISTINCT save_path FROM download_tasks WHERE save_path IS NOT NULL")
            results = cursor.fetchall()
            cursor.close()
            
            return [row[0] for row in results if row[0]]
            
        except Exception as e:
            self.logger.error(f"查询数据库文件路径失败: {e}")
            return []    def get_directory_size(self, directory: str) -> int:
        """计算目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except OSError:
                        continue
        except OSError:
            pass
        return total_size

    def cleanup_empty_directories(self, file_paths: List[str]):
        """清理空目录"""
        directories_to_check = set()
        
        for file_path in file_paths:
            dir_path = os.path.dirname(file_path)
            if dir_path != DOWNLOAD_DIR:
                directories_to_check.add(dir_path)
        
        for dir_path in directories_to_check:
            try:
                if os.path.exists(dir_path) and not os.listdir(dir_path):
                    os.rmdir(dir_path)
                    self.logger.info(f"删除空目录: {dir_path}")
            except OSError:
                pass

    def remove_aria2_task(self, gid: str) -> bool:
        """从aria2中移除任务"""
        try:
            # 尝试移除活跃任务
            result = self.call_aria2("aria2.remove", [gid])
            if "result" in result:
                self.logger.info(f"从aria2移除活跃任务: {gid}")
                return True
            
            # 尝试移除已停止的任务
            result = self.call_aria2("aria2.removeDownloadResult", [gid])
            if "result" in result:
                self.logger.info(f"从aria2移除已停止任务: {gid}")
                return True
                
        except Exception as e:
            self.logger.debug(f"移除aria2任务失败 {gid}: {e}")
        
        return False

    def clean_uploaded_files(self) -> Dict:
        """清理已上传的文件（核心功能1）"""
        self.logger.info("开始清理已上传的文件...")
        
        uploaded_tasks = self.get_uploaded_tasks()
        cleaned_files = []
        cleaned_size = 0
        failed_files = []
        
        for task in uploaded_tasks:
            save_path = task["save_path"]
            if not save_path:
                continue
                
            # 检查文件是否存在
            if os.path.exists(save_path):
                try:
                    file_size = os.path.getsize(save_path)
                    
                    # 删除文件
                    os.remove(save_path)
                    cleaned_files.append(save_path)
                    cleaned_size += file_size
                    
                    self.logger.info(f"删除已上传文件: {save_path} ({file_size / (1024*1024):.1f} MB)")
                    
                    # 尝试删除空目录
                    dir_path = os.path.dirname(save_path)
                    if dir_path != DOWNLOAD_DIR:
                        try:
                            if os.path.exists(dir_path) and not os.listdir(dir_path):
                                os.rmdir(dir_path)
                                self.logger.info(f"删除空目录: {dir_path}")
                        except OSError:
                            pass
                    
                    # 如果有aria2 GID，尝试从aria2中移除任务
                    if task["aria2_gid"]:
                        self.remove_aria2_task(task["aria2_gid"])
                        
                except OSError as e:
                    failed_files.append(f"{save_path}: {e}")
                    self.logger.error(f"删除已上传文件失败: {save_path} - {e}")
        
        return {
            "cleaned_files": cleaned_files,
            "cleaned_size": cleaned_size,
            "failed_files": failed_files
        }

    def clean_small_directories(self) -> Dict:
        """清理小于100MB的目录（核心功能2）"""
        self.logger.info("开始清理小目录...")
        
        cleaned_dirs = []
        cleaned_size = 0
        failed_dirs = []

        try:
            for item in os.listdir(DOWNLOAD_DIR):
                item_path = os.path.join(DOWNLOAD_DIR, item)

                if os.path.isdir(item_path):
                    dir_size = self.get_directory_size(item_path)

                    if dir_size < MIN_DIR_SIZE_TO_KEEP:
                        try:
                            shutil.rmtree(item_path)
                            cleaned_dirs.append(item_path)
                            cleaned_size += dir_size
                            self.logger.info(f"删除小目录: {item_path} ({dir_size / (1024*1024):.1f} MB)")
                        except OSError as e:
                            failed_dirs.append(f"{item_path}: {e}")
                            self.logger.error(f"删除目录失败: {item_path} - {e}")

        except OSError as e:
            self.logger.error(f"扫描目录失败: {e}")

        return {
            "cleaned_dirs": cleaned_dirs,
            "cleaned_size": cleaned_size,
            "failed_dirs": failed_dirs
        }    def clean_orphan_files(self) -> Dict:
        """清理孤儿文件（核心功能3）"""
        self.logger.info("开始清理孤儿文件...")
        
        cleaned_files = []
        cleaned_size = 0
        failed_cleanups = []
        
        # 获取数据库中的文件路径
        db_paths = set(self.get_database_file_paths())
        
        try:
            for root, dirs, files in os.walk(DOWNLOAD_DIR):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # 跳过aria2临时文件
                    if file.endswith('.aria2'):
                        continue
                        
                    # 检查文件是否在数据库中有记录
                    if file_path not in db_paths:
                        try:
                            file_size = os.path.getsize(file_path)
                            file_mtime = os.path.getmtime(file_path)
                            current_time = time.time()
                            file_age_hours = (current_time - file_mtime) / 3600
                            
                            # 只清理大于500MB且文件年龄超过6小时的孤儿文件
                            if file_size > ORPHAN_FILE_MIN_SIZE and file_age_hours > ORPHAN_FILE_MIN_AGE_HOURS:
                                os.remove(file_path)
                                cleaned_files.append(file_path)
                                cleaned_size += file_size
                                self.logger.info(f"删除孤儿文件: {file_path} ({file_size / (1024*1024):.1f} MB, 文件年龄: {file_age_hours:.1f}小时)")
                                
                                # 删除对应的目录（如果为空）
                                dir_path = os.path.dirname(file_path)
                                if dir_path != DOWNLOAD_DIR:
                                    try:
                                        if os.path.exists(dir_path) and not os.listdir(dir_path):
                                            os.rmdir(dir_path)
                                            self.logger.info(f"删除空目录: {dir_path}")
                                    except OSError:
                                        pass
                                        
                        except OSError as e:
                            failed_cleanups.append(f"{file_path}: {e}")
                            
        except Exception as e:
            self.logger.error(f"扫描孤儿文件失败: {e}")
        
        return {
            "cleaned_files": cleaned_files,
            "cleaned_size": cleaned_size,
            "failed_cleanups": failed_cleanups
        }

    def clean_fake_failed_tasks(self) -> Dict:
        """清理假失败任务（核心功能4）"""
        self.logger.info("开始清理假失败任务...")
        
        cleaned_files = []
        cleaned_size = 0
        failed_cleanups = []
        
        try:
            stopped_tasks = self.call_aria2("aria2.tellStopped", [0, 100])
            
            if "result" not in stopped_tasks:
                return {"cleaned_files": [], "cleaned_size": 0, "failed_cleanups": []}
            
            for task in stopped_tasks["result"]:
                if task.get("status") == "error" and task.get("errorCode") in ["18", "7"]:
                    files = task.get("files", [])
                    for file_info in files:
                        file_path = file_info.get("path", "")
                        if file_path and os.path.exists(file_path):
                            try:
                                file_size = os.path.getsize(file_path)
                                # 如果文件大于100MB，认为是有效下载，删除它
                                if file_size > 100 * 1024 * 1024:
                                    os.remove(file_path)
                                    cleaned_files.append(file_path)
                                    cleaned_size += file_size
                                    self.logger.info(f"删除假失败任务文件: {file_path} ({file_size / (1024*1024):.1f} MB)")
                                    
                                    # 删除对应的目录（如果为空）
                                    dir_path = os.path.dirname(file_path)
                                    if dir_path != DOWNLOAD_DIR:
                                        try:
                                            if os.path.exists(dir_path) and not os.listdir(dir_path):
                                                os.rmdir(dir_path)
                                                self.logger.info(f"删除空目录: {dir_path}")
                                        except OSError:
                                            pass
                                            
                            except OSError as e:
                                failed_cleanups.append(f"{file_path}: {e}")
                                
        except Exception as e:
            self.logger.error(f"清理假失败任务异常: {e}")
        
        return {
            "cleaned_files": cleaned_files,
            "cleaned_size": cleaned_size,
            "failed_cleanups": failed_cleanups
        }

    def get_disk_usage_report(self) -> Dict:
        """获取磁盘使用报告"""
        try:
            stat = shutil.disk_usage(DOWNLOAD_DIR)
            total_bytes = stat.total
            used_bytes = stat.used
            free_bytes = stat.free
            
            return {
                "total_gb": total_bytes / (1024**3),
                "used_gb": used_bytes / (1024**3),
                "free_gb": free_bytes / (1024**3),
                "usage_percent": (used_bytes / total_bytes) * 100
            }
        except Exception as e:
            self.logger.error(f"获取磁盘使用情况失败: {e}")
            return {}

    def comprehensive_cleanup(self) -> Dict:
        """执行综合清理"""
        self.logger.info("开始执行综合清理...")
        
        total_stats = {
            "uploaded_files": {},
            "small_directories": {},
            "orphan_files": {},
            "fake_failed_tasks": {},
            "total_cleaned_size": 0,
            "total_cleaned_files": 0
        }
        
        # 1. 清理已上传文件
        uploaded_result = self.clean_uploaded_files()
        total_stats["uploaded_files"] = uploaded_result
        total_stats["total_cleaned_size"] += uploaded_result["cleaned_size"]
        total_stats["total_cleaned_files"] += len(uploaded_result["cleaned_files"])
        
        # 2. 清理小目录
        small_dirs_result = self.clean_small_directories()
        total_stats["small_directories"] = small_dirs_result
        total_stats["total_cleaned_size"] += small_dirs_result["cleaned_size"]
        
        # 3. 清理孤儿文件
        orphan_result = self.clean_orphan_files()
        total_stats["orphan_files"] = orphan_result
        total_stats["total_cleaned_size"] += orphan_result["cleaned_size"]
        total_stats["total_cleaned_files"] += len(orphan_result["cleaned_files"])
        
        # 4. 清理假失败任务
        fake_failed_result = self.clean_fake_failed_tasks()
        total_stats["fake_failed_tasks"] = fake_failed_result
        total_stats["total_cleaned_size"] += fake_failed_result["cleaned_size"]
        total_stats["total_cleaned_files"] += len(fake_failed_result["cleaned_files"])
        
        self.logger.info(f"综合清理完成: 清理了 {total_stats['total_cleaned_files']} 个文件，释放 {total_stats['total_cleaned_size']/(1024*1024*1024):.2f} GB")
        
        return total_stats

if __name__ == "__main__":
    import sys
    
    cleaner = Aria2CleanerOptimized()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "uploaded":
            # 清理已上传文件
            result = cleaner.clean_uploaded_files()
            print(f"清理已上传文件: {len(result['cleaned_files'])} 个文件，释放 {result['cleaned_size']/(1024*1024*1024):.2f} GB")
            
        elif command == "orphan":
            # 清理孤儿文件
            result = cleaner.clean_orphan_files()
            print(f"清理孤儿文件: {len(result['cleaned_files'])} 个文件，释放 {result['cleaned_size']/(1024*1024*1024):.2f} GB")
            
        elif command == "small":
            # 清理小目录
            result = cleaner.clean_small_directories()
            print(f"清理小目录: {len(result['cleaned_dirs'])} 个目录，释放 {result['cleaned_size']/(1024*1024*1024):.2f} GB")
            
        elif command == "fake":
            # 清理假失败任务
            result = cleaner.clean_fake_failed_tasks()
            print(f"清理假失败任务: {len(result['cleaned_files'])} 个文件，释放 {result['cleaned_size']/(1024*1024*1024):.2f} GB")
            
        elif command == "all":
            # 综合清理
            result = cleaner.comprehensive_cleanup()
            print(f"综合清理完成: {result['total_cleaned_files']} 个文件，释放 {result['total_cleaned_size']/(1024*1024*1024):.2f} GB")
            
        elif command == "disk":
            # 显示磁盘使用情况
            disk_info = cleaner.get_disk_usage_report()
            if disk_info:
                print(f"磁盘使用情况:")
                print(f"  总空间: {disk_info['total_gb']:.1f} GB")
                print(f"  已使用: {disk_info['used_gb']:.1f} GB")
                print(f"  可用空间: {disk_info['free_gb']:.1f} GB")
                print(f"  使用率: {disk_info['usage_percent']:.1f}%")
        else:
            print("用法:")
            print("  python3 aria2_cleaner_optimized.py uploaded  # 清理已上传文件")
            print("  python3 aria2_cleaner_optimized.py orphan    # 清理孤儿文件")
            print("  python3 aria2_cleaner_optimized.py small     # 清理小目录")
            print("  python3 aria2_cleaner_optimized.py fake      # 清理假失败任务")
            print("  python3 aria2_cleaner_optimized.py all       # 综合清理")
            print("  python3 aria2_cleaner_optimized.py disk      # 显示磁盘使用情况")
    else:
        # 默认综合清理
        result = cleaner.comprehensive_cleanup()
        print(f"综合清理完成: {result['total_cleaned_files']} 个文件，释放 {result['total_cleaned_size']/(1024*1024*1024):.2f} GB")