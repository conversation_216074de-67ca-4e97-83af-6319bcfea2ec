#!/usr/bin/env python3
"""
Aria2智能存储管理器
专门针对512GB存储限制的预防性存储管理
"""

import os
import json
import requests
import time
import logging
import shutil
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Tuple
from pathlib import Path

# 配置
ARIA2_RPC_URL = "http://localhost:6800/jsonrpc"
ARIA2_SECRET = "aria2secret"
DOWNLOAD_DIR = "/downloads"

# 存储阈值配置（512GB = 549,755,813,888 字节）
TOTAL_STORAGE_GB = 512
TOTAL_STORAGE_BYTES = TOTAL_STORAGE_GB * 1024 * 1024 * 1024

# 存储管理阈值
WARNING_THRESHOLD = 0.80    # 80% - 410GB
PAUSE_THRESHOLD = 0.90      # 90% - 460GB  
EMERGENCY_THRESHOLD = 0.95  # 95% - 486GB

# 动态并发调整
CONCURRENT_LIMITS = {
    "high": 8,      # >200GB可用空间
    "medium": 5,    # 100-200GB可用空间
    "low": 3,       # 50-100GB可用空间
    "critical": 1   # <50GB可用空间
}

class StorageManager:
    def __init__(self):
        self.setup_logging()
        self.session = requests.Session()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/var/log/aria2_storage_manager.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def get_storage_info(self) -> Dict[str, float]:
        """获取存储信息"""
        try:
            stat = shutil.disk_usage(DOWNLOAD_DIR)
            total_bytes = stat.total
            used_bytes = stat.used
            free_bytes = stat.free
            
            return {
                "total_gb": total_bytes / (1024**3),
                "used_gb": used_bytes / (1024**3),
                "free_gb": free_bytes / (1024**3),
                "usage_percent": (used_bytes / total_bytes) * 100,
                "free_percent": (free_bytes / total_bytes) * 100
            }
        except Exception as e:
            self.logger.error(f"获取存储信息失败: {e}")
            return {}

    def call_aria2(self, method: str, params: List[Any] = None) -> Dict:
        """调用aria2 RPC接口"""
        if params is None:
            params = []
        
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": [f"token:{ARIA2_SECRET}"] + params
        }
        
        try:
            response = self.session.post(ARIA2_RPC_URL, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Aria2 RPC调用失败: {e}")
            return {"error": str(e)}

    def get_active_downloads(self) -> List[Dict]:
        """获取活跃下载任务"""
        result = self.call_aria2("aria2.tellActive")
        if "result" in result:
            return result["result"]
        return []

    def get_waiting_downloads(self) -> List[Dict]:
        """获取等待中的下载任务"""
        result = self.call_aria2("aria2.tellWaiting", [0, 1000])
        if "result" in result:
            return result["result"]
        return []

    def pause_task(self, gid: str) -> bool:
        """暂停任务"""
        result = self.call_aria2("aria2.pause", [gid])
        return "result" in result

    def unpause_task(self, gid: str) -> bool:
        """恢复任务"""
        result = self.call_aria2("aria2.unpause", [gid])
        return "result" in result

    def remove_task(self, gid: str) -> bool:
        """移除任务"""
        result = self.call_aria2("aria2.remove", [gid])
        return "result" in result

    def change_global_option(self, options: Dict[str, str]) -> bool:
        """修改全局选项"""
        result = self.call_aria2("aria2.changeGlobalOption", [options])
        return "result" in result

    def get_slow_tasks(self, speed_threshold: int = 50 * 1024) -> List[Dict]:
        """获取低速任务"""
        active_tasks = self.get_active_downloads()
        slow_tasks = []
        
        for task in active_tasks:
            try:
                download_speed = int(task.get("downloadSpeed", "0"))
                if download_speed < speed_threshold and download_speed > 0:
                    slow_tasks.append(task)
            except (ValueError, TypeError):
                continue
                
        return slow_tasks

    def get_large_incomplete_files(self, min_size_gb: float = 1.0) -> List[Tuple[str, float]]:
        """获取大型未完成文件"""
        large_files = []
        min_size_bytes = min_size_gb * 1024 * 1024 * 1024
        
        try:
            for root, dirs, files in os.walk(DOWNLOAD_DIR):
                for file in files:
                    if file.endswith(('.aria2', '.part')):
                        file_path = os.path.join(root, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            if file_size >= min_size_bytes:
                                large_files.append((file_path, file_size / (1024**3)))
                        except OSError:
                            continue
        except Exception as e:
            self.logger.error(f"扫描大文件失败: {e}")
            
        return sorted(large_files, key=lambda x: x[1], reverse=True)

    def calculate_optimal_concurrent(self, free_gb: float) -> int:
        """根据可用空间计算最优并发数"""
        if free_gb > 200:
            return CONCURRENT_LIMITS["high"]
        elif free_gb > 100:
            return CONCURRENT_LIMITS["medium"]
        elif free_gb > 50:
            return CONCURRENT_LIMITS["low"]
        else:
            return CONCURRENT_LIMITS["critical"]

    def emergency_cleanup(self) -> Dict[str, Any]:
        """紧急清理"""
        self.logger.warning("执行紧急存储清理")
        
        cleanup_stats = {
            "removed_files": [],
            "freed_space_gb": 0,
            "paused_tasks": [],
            "removed_tasks": []
        }
        
        # 1. 暂停所有活跃任务
        active_tasks = self.get_active_downloads()
        for task in active_tasks:
            gid = task.get("gid")
            if gid and self.pause_task(gid):
                cleanup_stats["paused_tasks"].append(gid)
                
        # 2. 删除大型未完成文件
        large_files = self.get_large_incomplete_files(0.5)  # 500MB以上
        for file_path, size_gb in large_files[:10]:  # 最多删除10个最大文件
            try:
                os.remove(file_path)
                cleanup_stats["removed_files"].append(file_path)
                cleanup_stats["freed_space_gb"] += size_gb
                self.logger.info(f"紧急删除大文件: {file_path} ({size_gb:.1f}GB)")
            except OSError as e:
                self.logger.error(f"删除文件失败: {file_path} - {e}")
                
        # 3. 移除错误任务
        stopped_tasks = self.call_aria2("aria2.tellStopped", [0, 100])
        if "result" in stopped_tasks:
            for task in stopped_tasks["result"]:
                if task.get("status") == "error":
                    gid = task.get("gid")
                    if gid:
                        self.call_aria2("aria2.removeDownloadResult", [gid])
                        cleanup_stats["removed_tasks"].append(gid)
        
        return cleanup_stats

    def smart_storage_management(self) -> Dict[str, Any]:
        """智能存储管理主逻辑"""
        storage_info = self.get_storage_info()
        if not storage_info:
            return {"error": "无法获取存储信息"}
            
        usage_percent = storage_info["usage_percent"]
        free_gb = storage_info["free_gb"]
        
        self.logger.info(f"存储使用率: {usage_percent:.1f}%, 可用空间: {free_gb:.1f}GB")
        
        actions = []
        
        # 紧急清理阈值
        if usage_percent >= EMERGENCY_THRESHOLD * 100:
            self.logger.critical(f"存储空间严重不足! 使用率: {usage_percent:.1f}%")
            cleanup_result = self.emergency_cleanup()
            actions.append(f"紧急清理: 释放 {cleanup_result['freed_space_gb']:.1f}GB")
            
        # 暂停新任务阈值
        elif usage_percent >= PAUSE_THRESHOLD * 100:
            self.logger.warning(f"存储空间不足，暂停新任务。使用率: {usage_percent:.1f}%")
            
            # 暂停等待中的任务
            waiting_tasks = self.get_waiting_downloads()
            paused_count = 0
            for task in waiting_tasks[:5]:  # 最多暂停5个等待任务
                gid = task.get("gid")
                if gid and self.pause_task(gid):
                    paused_count += 1
                    
            if paused_count > 0:
                actions.append(f"暂停 {paused_count} 个等待任务")
                
            # 暂停低速任务
            slow_tasks = self.get_slow_tasks(30 * 1024)  # 30KB/s
            for task in slow_tasks[:3]:  # 最多暂停3个低速任务
                gid = task.get("gid")
                if gid and self.pause_task(gid):
                    actions.append(f"暂停低速任务: {gid}")
                    
        # 警告阈值 - 调整并发数
        elif usage_percent >= WARNING_THRESHOLD * 100:
            self.logger.warning(f"存储空间警告。使用率: {usage_percent:.1f}%")
            
            optimal_concurrent = self.calculate_optimal_concurrent(free_gb)
            current_active = len(self.get_active_downloads())
            
            if current_active > optimal_concurrent:
                # 降低并发数
                if self.change_global_option({"max-concurrent-downloads": str(optimal_concurrent)}):
                    actions.append(f"调整并发数: {current_active} -> {optimal_concurrent}")
                    
        # 正常状态 - 优化并发数
        else:
            optimal_concurrent = self.calculate_optimal_concurrent(free_gb)
            if self.change_global_option({"max-concurrent-downloads": str(optimal_concurrent)}):
                actions.append(f"优化并发数: {optimal_concurrent}")
        
        return {
            "storage_info": storage_info,
            "actions": actions,
            "status": "success"
        }

    def monitor_and_manage(self, interval: int = 60):
        """持续监控和管理"""
        self.logger.info("Aria2智能存储管理器启动")
        
        while True:
            try:
                result = self.smart_storage_management()
                if result.get("actions"):
                    self.logger.info(f"执行管理操作: {', '.join(result['actions'])}")
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                self.logger.info("收到停止信号，退出监控")
                break
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)

if __name__ == "__main__":
    import sys
    
    manager = StorageManager()
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "monitor":
            # 持续监控模式
            manager.monitor_and_manage(interval=60)
        elif command == "once":
            # 单次管理
            result = manager.smart_storage_management()
            print(f"管理结果: {result}")
        elif command == "emergency":
            # 紧急清理
            result = manager.emergency_cleanup()
            print(f"紧急清理结果: {result}")
        elif command == "status":
            # 显示存储状态
            storage_info = manager.get_storage_info()
            if storage_info:
                print(f"存储状态:")
                print(f"  总空间: {storage_info['total_gb']:.1f} GB")
                print(f"  已使用: {storage_info['used_gb']:.1f} GB")
                print(f"  可用空间: {storage_info['free_gb']:.1f} GB")
                print(f"  使用率: {storage_info['usage_percent']:.1f}%")
        else:
            print("用法:")
            print("  python3 aria2_storage_manager.py monitor     # 持续监控模式")
            print("  python3 aria2_storage_manager.py once       # 单次管理")
            print("  python3 aria2_storage_manager.py emergency  # 紧急清理")
            print("  python3 aria2_storage_manager.py status     # 显示存储状态")
    else:
        # 默认单次管理
        result = manager.smart_storage_management()
        print(f"管理结果: {result}")