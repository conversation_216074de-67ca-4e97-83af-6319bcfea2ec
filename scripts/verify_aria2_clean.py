#!/usr/bin/env python3
"""
Aria2 清理验证脚本
验证所有缓存是否已被清理
"""

import requests
import json
from pathlib import Path

def verify_aria2_clean():
    """验证aria2清理状态"""
    print("🔍 验证 Aria2 清理状态...")
    print("=" * 40)
    
    # 1. 检查RPC连接
    try:
        response = requests.post("http://localhost:6800/jsonrpc", json={
            "jsonrpc": "2.0",
            "method": "aria2.getVersion",
            "params": ["token:aria2secret"],
            "id": "test"
        }, timeout=5)
        
        if response.status_code == 200:
            version_info = response.json().get("result", {})
            print(f"✅ Aria2 服务正常运行 (版本: {version_info.get('version', 'Unknown')})")
        else:
            print("❌ Aria2 RPC 连接失败")
            return False
    except Exception as e:
        print(f"❌ Aria2 RPC 连接错误: {e}")
        return False
    
    # 2. 检查活动任务
    try:
        response = requests.post("http://localhost:6800/jsonrpc", json={
            "jsonrpc": "2.0",
            "method": "aria2.tellActive",
            "params": ["token:aria2secret"],
            "id": "active"
        })
        
        if response.status_code == 200:
            active_tasks = response.json().get("result", [])
            print(f"✅ 活动任务数量: {len(active_tasks)}")
        else:
            print("⚠️  无法获取活动任务信息")
    except Exception as e:
        print(f"⚠️  获取活动任务错误: {e}")
    
    # 3. 检查等待队列
    try:
        response = requests.post("http://localhost:6800/jsonrpc", json={
            "jsonrpc": "2.0",
            "method": "aria2.tellWaiting",
            "params": ["token:aria2secret", 0, 100],
            "id": "waiting"
        })
        
        if response.status_code == 200:
            waiting_tasks = response.json().get("result", [])
            print(f"✅ 等待任务数量: {len(waiting_tasks)}")
        else:
            print("⚠️  无法获取等待任务信息")
    except Exception as e:
        print(f"⚠️  获取等待任务错误: {e}")
    
    # 4. 检查已停止任务
    try:
        response = requests.post("http://localhost:6800/jsonrpc", json={
            "jsonrpc": "2.0",
            "method": "aria2.tellStopped",
            "params": ["token:aria2secret", 0, 100],
            "id": "stopped"
        })
        
        if response.status_code == 200:
            stopped_tasks = response.json().get("result", [])
            print(f"✅ 已停止任务记录: {len(stopped_tasks)}")
        else:
            print("⚠️  无法获取已停止任务信息")
    except Exception as e:
        print(f"⚠️  获取已停止任务错误: {e}")
    
    # 5. 检查session文件
    session_files = [
        "/var/lib/docker/volumes/javapicom_aria2_config/_data/aria2.session",
        "/www/wwwroot/JAVAPI.COM/config/aria2.session"
    ]
    
    for session_file in session_files:
        session_path = Path(session_file)
        if session_path.exists():
            size = session_path.stat().st_size
            print(f"✅ Session文件 {session_path.name}: {size} 字节")
        else:
            print(f"⚠️  Session文件不存在: {session_file}")
    
    # 6. 检查DHT文件
    dht_files = [
        "/var/lib/docker/volumes/javapicom_aria2_config/_data/dht.dat",
        "/var/lib/docker/volumes/javapicom_aria2_config/_data/dht6.dat"
    ]
    
    for dht_file in dht_files:
        dht_path = Path(dht_file)
        if dht_path.exists():
            size = dht_path.stat().st_size
            print(f"✅ DHT文件 {dht_path.name}: {size} 字节 (已重新生成)")
        else:
            print(f"✅ DHT文件已清理: {dht_file}")
    
    print()
    print("🎉 Aria2 缓存清理验证完成！")
    print("📝 说明:")
    print("   - Session文件为0字节表示下载历史已清空")
    print("   - DHT文件重新生成是正常现象")
    print("   - 所有任务队列应该为空")
    
    return True

if __name__ == "__main__":
    verify_aria2_clean()