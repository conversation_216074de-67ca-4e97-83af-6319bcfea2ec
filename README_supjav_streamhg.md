# SupJAV到StreamHG远程下载工具

这个工具可以从supjav.com提取视频直链，并通过StreamHG的远程下载功能将视频上传到云端。

## 功能特点

- 🎯 自动提取supjav.com的视频直链
- ☁️ 通过StreamHG远程下载功能上传到云端
- 🔄 支持多种视频格式（MP4, M3U8等）
- 🛡️ 使用CloudScraper绕过反爬虫保护
- 📊 详细的日志和错误处理
- 🔁 支持批量处理

## 安装依赖

```bash
pip install requests cloudscraper lxml
```

## 配置

编辑 `streamhg_config.json` 文件：

```json
{
  "streamhg_api_key": "你的StreamHG API密钥",
  "default_folder_id": null,
  "wait_for_completion": true,
  "max_wait_time": 300,
  "request_delay": 5,
  "retry_attempts": 3,
  "log_level": "INFO"
}
```

### 配置说明

- `streamhg_api_key`: StreamHG API密钥（必需）
- `default_folder_id`: 默认上传文件夹ID（可选）
- `wait_for_completion`: 是否等待上传完成
- `max_wait_time`: 最大等待时间（秒）
- `request_delay`: 请求间隔（秒）
- `retry_attempts`: 重试次数
- `log_level`: 日志级别

## 使用方法

### 单个URL处理

```bash
python supjav_to_streamhg.py https://supjav.com/357979.html
```

### 指定文件夹

```bash
python supjav_to_streamhg.py https://supjav.com/357979.html 25
```

### 仅提取视频链接（测试）

```bash
python supjav_extractor.py
```

## 工作流程

1. **页面分析**: 使用CloudScraper获取supjav页面内容
2. **链接提取**: 通过多种方法提取视频直链：
   - HTML video标签解析
   - JavaScript代码分析
   - iframe嵌入播放器
   - 播放器配置解析
3. **链接验证**: 验证提取的链接是否为有效视频
4. **远程上传**: 调用StreamHG API进行远程下载
5. **状态监控**: 可选择等待上传完成

## 支持的视频格式

- MP4 (优先)
- M3U8 (流媒体)
- WebM
- AVI
- MKV
- MOV
- FLV

## 错误处理

工具包含完善的错误处理机制：

- 网络连接失败自动重试
- 页面结构变化的兼容性处理
- 视频链接失效检测
- StreamHG API错误处理

## 日志输出

工具会输出详细的操作日志：

```
2024-01-01 12:00:00 - INFO - 开始提取视频链接: https://supjav.com/357979.html
2024-01-01 12:00:01 - INFO - 视频标题: Sample Video Title
2024-01-01 12:00:02 - INFO - 找到有效视频链接: https://example.com/video.mp4
2024-01-01 12:00:03 - INFO - 开始上传视频到StreamHG
2024-01-01 12:00:05 - INFO - 上传成功，文件代码: abc123def
```

## 批量处理示例

```python
from supjav_to_streamhg import SupJavToStreamHG

# 创建处理器
processor = SupJavToStreamHG("你的API密钥")

# 批量处理URL列表
urls = [
    "https://supjav.com/357979.html",
    "https://supjav.com/357980.html",
    "https://supjav.com/357981.html"
]

results = processor.batch_process(urls, folder_id=25)

for result in results:
    if result['success']:
        print(f"✅ {result['video_title']}: {result['streamhg_url']}")
    else:
        print(f"❌ 失败: {result['message']}")
```

## 注意事项

1. **合法使用**: 请确保遵守相关网站的使用条款
2. **请求频率**: 工具内置了请求延迟，避免过于频繁的请求
3. **网络环境**: 某些地区可能需要代理才能访问supjav.com
4. **API限制**: StreamHG API可能有使用限制，请注意配额

## 故障排除

### 常见问题

1. **无法访问supjav.com**
   - 检查网络连接
   - 尝试使用代理
   - 确认网站是否可访问

2. **提取不到视频链接**
   - 页面可能使用了新的播放器
   - 视频可能需要登录才能观看
   - 检查页面是否正常加载

3. **StreamHG上传失败**
   - 检查API密钥是否正确
   - 确认视频链接是否有效
   - 检查网络连接

4. **上传速度慢**
   - 这是正常现象，StreamHG需要时间下载视频
   - 可以设置 `wait_for_completion: false` 不等待完成

## 技术原理

### 视频链接提取

工具使用多种技术提取视频链接：

1. **HTML解析**: 使用lxml解析页面结构
2. **JavaScript分析**: 正则表达式匹配JS中的视频URL
3. **Base64解码**: 处理编码的视频链接
4. **iframe递归**: 深入分析嵌入的播放器

### 反爬虫绕过

- 使用CloudScraper绕过Cloudflare保护
- 模拟真实浏览器的HTTP头
- 合理的请求间隔和重试机制

### StreamHG集成

- 使用官方API进行远程下载
- 支持文件夹组织和标题设置
- 实时状态监控和错误处理

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持supjav.com视频链接提取
- 集成StreamHG远程下载功能
- 完善的错误处理和日志系统
