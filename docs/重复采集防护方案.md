# 重复采集防护方案

## 🚨 问题分析

### 原有问题
1. **数据库层面**：`jav_magnets.magnet_url` 和 `download_tasks.magnet_uri` 字段没有唯一性约束
2. **应用层面**：scraper重新采集时没有检查磁力链接是否已存在
3. **下载层面**：可能创建重复的aria2下载任务

### 风险影响
- 数据库中存储大量重复磁力链接
- 创建重复的aria2下载任务，浪费带宽和存储
- 影响系统性能和稳定性

## ✅ 解决方案

### 1. 数据库层面防护

#### 添加唯一性约束
```sql
-- 执行迁移脚本
-- migrations/007_add_unique_constraints.sql

-- 磁力链接URL唯一性约束
ALTER TABLE jav_magnets 
ADD CONSTRAINT uk_jav_magnets_magnet_url 
UNIQUE (magnet_url);

-- 下载任务磁力链接唯一性约束
ALTER TABLE download_tasks 
ADD CONSTRAINT uk_download_tasks_magnet_uri 
UNIQUE (magnet_uri);

-- 影片+磁力链接复合唯一性约束
ALTER TABLE jav_magnets 
ADD CONSTRAINT uk_jav_magnets_movie_magnet 
UNIQUE (movie_id, magnet_url);
```

#### 清理重复数据
```sql
-- 自动清理现有重复数据（保留ID最小的记录）
DELETE FROM jav_magnets 
WHERE id NOT IN (
    SELECT MIN(id) 
    FROM (SELECT id, magnet_url FROM jav_magnets) AS temp_table
    GROUP BY magnet_url
);
```

### 2. 应用层面防护

#### 磁力链接保存时检查重复
```go
// cmd/complete_javbus_scraper/helpers.go
// 修改后的保存逻辑

err = repo.JAVMagnet().Create(magnet)
if err != nil {
    // 如果是唯一性约束错误，说明是重复数据
    if strings.Contains(err.Error(), "duplicate") || 
       strings.Contains(err.Error(), "unique") ||
       strings.Contains(err.Error(), "UNIQUE constraint failed") {
        duplicateCount++
        fmt.Printf("        ⚠️  磁力链接已存在，跳过: %s\n", magnetInfo.FileName)
    } else {
        fmt.Printf("        ⚠️  保存磁力链接失败: %v\n", err)
    }
    continue
}
```

#### aria2任务创建时检查重复
```go
// cmd/complete_javbus_scraper/helpers_extended.go
// 修改后的任务创建逻辑

// 检查是否已有相同磁力链接的活跃任务
activeTasks, err := aria2Service.GetActiveTasks()
if err == nil {
    for _, task := range activeTasks {
        if task.InfoHash != "" && strings.Contains(magnet.MagnetURL, task.InfoHash) {
            logger.Warnf("磁力链接已在下载中，跳过重复任务")
            return nil // 不是错误，只是跳过
        }
    }
}
```

### 3. 重复采集策略

#### 影片级别的重复检查
```go
// 检查影片是否已存在且最近更新过
if existingMovie != nil && j.config.SkipRecentMovies {
    if existingMovie.UpdatedAt.After(time.Now().Add(-j.config.RecentThreshold)) {
        stats.SkippedMovies++
        return nil // 跳过最近更新的影片
    }
}
```

#### 磁力链接级别的智能去重
```go
// 改进的相似度检测
func isSimilarMagnet(magnet1, magnet2 *ScoredMagnet) bool {
    // 如果一个有字幕一个没有，且质量相同，不认为相似（允许同时下载）
    if magnet1.HasSubtitle != magnet2.HasSubtitle && magnet1.Quality == magnet2.Quality {
        // 但如果文件大小几乎完全相同（差异小于5%），仍然认为相似
        if magnet1.FileSize > 0 && magnet2.FileSize > 0 {
            diff := float64(abs(magnet1.FileSize-magnet2.FileSize)) / float64(max(magnet1.FileSize, magnet2.FileSize))
            if diff < 0.05 { // 5%以内认为是同一个文件
                return true
            }
        }
        return false // 字幕状态不同，质量相同，允许同时下载
    }
    
    // 其他情况的相似度检测...
}
```

## 🎯 实施步骤

### 1. 立即执行数据库迁移
```bash
# 在数据库中执行迁移脚本
mysql -u root -p javapi < migrations/007_add_unique_constraints.sql
```

### 2. 重新编译scraper
```bash
cd /www/wwwroot/JAVAPI.COM
go build -o scraper cmd/complete_javbus_scraper/*.go
```

### 3. 测试重复采集防护
```bash
# 对同一影片进行多次采集，验证去重效果
./scraper --movie-code VEC-708 --test-mode
```

## 📊 预期效果

### 数据库层面
- ✅ 防止重复磁力链接插入
- ✅ 防止重复下载任务创建
- ✅ 自动清理历史重复数据

### 应用层面
- ✅ 智能跳过已存在的磁力链接
- ✅ 避免创建重复的aria2任务
- ✅ 提供详细的重复检测日志

### 性能优化
- ✅ 减少数据库存储空间
- ✅ 避免重复下载浪费带宽
- ✅ 提高系统整体性能

## 🔧 监控和维护

### 定期清理重复数据
```sql
-- 可以定期执行的清理脚本
CALL CleanDuplicateMagnets();
```

### 监控重复率
```sql
-- 检查重复磁力链接数量
SELECT 
    COUNT(*) as total_magnets,
    COUNT(DISTINCT magnet_url) as unique_magnets,
    (COUNT(*) - COUNT(DISTINCT magnet_url)) as duplicates
FROM jav_magnets;
```

### 日志监控
- 监控scraper日志中的"重复"、"跳过"关键词
- 统计重复检测的效果和频率

## 🎉 总结

通过以上三层防护机制：
1. **数据库约束**：从根本上防止重复数据
2. **应用逻辑**：智能检测和跳过重复操作
3. **任务管理**：避免重复的下载任务

现在scraper可以安全地进行重复采集，不会造成大量重复的aria2下载任务和数据库冗余。