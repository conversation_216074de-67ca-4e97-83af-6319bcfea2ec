# 🧹 Aria2智能清理器使用指南

## 🎯 功能说明

**核心功能**：删除慢速任务的文件但保留任务记录，释放硬盘空间

**工作原理**：
1. 🔍 **监控任务速度**：检测下载速度低于50KB/s的任务
2. ⏰ **超时判断**：慢速任务持续5分钟后触发清理
3. ⏸️ **暂停任务**：停止下载但保留aria2任务记录
4. 🗑️ **删除文件**：删除本地文件释放硬盘空间
5. 📝 **记录状态**：保存清理记录，支持后续重新下载

## 🚀 快速开始

### 1. 启动后台监控
```bash
cd /www/wwwroot/JAVAPI.COM
./scripts/start_cleaner.sh start
```

### 2. 查看状态
```bash
./scripts/start_cleaner.sh status
```

### 3. 执行单次清理
```bash
./scripts/start_cleaner.sh once
```

## 📋 命令详解

### 基础命令
```bash
# 启动后台监控（推荐）
./scripts/start_cleaner.sh start

# 停止监控
./scripts/start_cleaner.sh stop

# 查看运行状态
./scripts/start_cleaner.sh status

# 执行单次清理
./scripts/start_cleaner.sh once
```

### 管理命令
```bash
# 查看已清理的任务
./scripts/start_cleaner.sh list

# 查看磁盘使用情况
./scripts/start_cleaner.sh disk

# 重新启动指定任务
./scripts/start_cleaner.sh restart <GID>

# 查看实时日志
./scripts/start_cleaner.sh log
```

## 🔧 配置参数

可以在脚本中修改以下参数：

```python
# 性能阈值配置
MIN_SPEED_THRESHOLD = 50 * 1024  # 50KB/s 最低速度阈值
SLOW_TASK_TIMEOUT = 300  # 5分钟低速超时
MIN_FILE_SIZE_TO_CLEAN = 100 * 1024 * 1024  # 100MB以上才清理
```

## 📊 使用示例

### 场景1：日常监控
```bash
# 启动后台监控
./scripts/start_cleaner.sh start

# 查看磁盘状态
./scripts/start_cleaner.sh disk
# 输出：
# 磁盘使用情况:
#   总空间: 502.8 GB
#   已使用: 160.4 GB
#   可用空间: 342.5 GB
#   使用率: 31.9%
```

### 场景2：手动清理
```bash
# 立即检查并清理慢速任务
./scripts/start_cleaner.sh once
# 输出：清理完成: 清理了 2 个任务，释放 15.6 GB
```

### 场景3：任务管理
```bash
# 查看已清理的任务
./scripts/start_cleaner.sh list
# 输出：
# 已清理任务 (3 个):
#   - mizd-464 (GID: abc123) - 释放 8.5 GB
#   - ssis-123 (GID: def456) - 释放 4.2 GB
#   - pred-789 (GID: ghi789) - 释放 2.9 GB

# 重新启动某个任务
./scripts/start_cleaner.sh restart abc123
# 输出：任务 abc123 重新启动成功
```

## 📈 监控日志

### 查看实时日志
```bash
./scripts/start_cleaner.sh log
```

### 日志文件位置
- **清理器日志**：`/www/wwwroot/JAVAPI.COM/logs/aria2_cleaner.log`
- **运行日志**：`/www/wwwroot/JAVAPI.COM/logs/cleaner.log`

### 日志示例
```
2025-06-29 09:47:08 - INFO - 检测到慢速任务: mizd-464 (速度: 24.8 KB/s)
2025-06-29 09:52:15 - INFO - 已暂停慢速任务: mizd-464 (速度: 24.8 KB/s)
2025-06-29 09:52:16 - INFO - 删除文件: /downloads/mizd-464/mizd-464-1.mp4 (8500.0 MB)
2025-06-29 09:52:16 - INFO - 清理完成: mizd-464 - 释放空间 8.50 GB
```

## ⚠️ 注意事项

### 安全提醒
1. **文件会被删除**：清理操作会真正删除文件，请确认理解
2. **任务可恢复**：aria2任务记录保留，可以重新下载
3. **备份重要数据**：建议定期备份重要文件

### 最佳实践
1. **后台运行**：建议使用 `start` 命令后台监控
2. **定期检查**：使用 `list` 命令查看清理历史
3. **磁盘监控**：使用 `disk` 命令监控空间使用
4. **合理配置**：根据需要调整速度阈值和超时时间

## 🔄 工作流程

```
下载任务启动
     ↓
监控下载速度
     ↓
速度 < 50KB/s？ ──否──→ 继续监控
     ↓ 是
持续5分钟？ ──否──→ 继续监控
     ↓ 是
暂停任务
     ↓
删除本地文件
     ↓
记录清理信息
     ↓
释放硬盘空间 ✅
```

## 🆘 故障排除

### 常见问题

**Q: 脚本无法连接aria2？**
A: 检查aria2是否运行，RPC端口是否正确（默认6800）

**Q: 清理后如何恢复任务？**
A: 使用 `./scripts/start_cleaner.sh restart <GID>` 命令

**Q: 如何修改速度阈值？**
A: 编辑脚本中的 `MIN_SPEED_THRESHOLD` 参数

**Q: 清理器占用资源多吗？**
A: 很少，每2分钟检查一次，几乎不占用系统资源

### 日志调试
```bash
# 查看详细日志
tail -f /www/wwwroot/JAVAPI.COM/logs/aria2_cleaner.log

# 检查错误信息
grep ERROR /www/wwwroot/JAVAPI.COM/logs/aria2_cleaner.log
```

## 🎉 效果预期

使用智能清理器后，您将获得：

- ✅ **硬盘空间释放**：自动清理慢速任务文件
- ✅ **带宽优化**：专注于快速下载任务
- ✅ **任务保留**：所有任务记录完整保存
- ✅ **灵活恢复**：随时重新启动任务
- ✅ **自动化管理**：无需手动干预

**预期效果**：硬盘使用率降低20-50%，下载效率提升30-80%！