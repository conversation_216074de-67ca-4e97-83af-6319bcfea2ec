# JAV采集和上传系统文件管理优化方案

## 🔍 问题分析总结

### 1. 文件删除策略问题
- **问题**：已完成上传且有播放链接的文件不会被自动删除
- **影响**：大量磁盘空间被已上传文件占用（如UBJ-001占用5.25GB）
- **根源**：`filterNewVideos`方法中对已完成任务直接跳过，不进入删除流程

### 2. 重复下载问题
- **问题**：`checkCompletedDownloadTask`函数为空实现，无法检测已完成的下载任务
- **影响**：相同影片被重复下载，浪费带宽和存储空间
- **根源**：缺少真正的数据库查询逻辑

### 3. 资源利用率低
- **问题**：系统无法主动清理已完成的文件
- **影响**：磁盘空间持续增长，影响系统性能

## ✅ 已实施的优化方案

### 1. 修复重复下载检查
```go
// 修复后的checkCompletedDownloadTask函数
func checkCompletedDownloadTask(movieCode string) (bool, error) {
    cfg, err := config.Load()
    if err != nil {
        return false, fmt.Errorf("加载配置失败: %w", err)
    }

    db, err := database.New(cfg.Database)
    if err != nil {
        return false, fmt.Errorf("连接数据库失败: %w", err)
    }

    // 查询是否有已完成的下载任务
    var count int64
    err = db.Model(&model.DownloadTask{}).
        Where("task_name LIKE ? AND status = ? AND processing_status = ? AND (play_url IS NOT NULL AND play_url != '')", 
            "%"+movieCode+"%", "completed", "completed").
        Count(&count)
    
    return count > 0, nil
}
```

### 2. 优化文件删除策略
```go
// 修改后的filterNewVideos逻辑
if hasPlayURL && isCompleted {
    // 🔥 优化：已成功上传且有播放链接的文件，主动删除以释放磁盘空间
    if s.config.FileProcessing.AutoUpload.DeleteAfterUpload {
        logger.Infof("发现已完成上传的文件，主动删除以释放空间: %s", video.Path)
        s.deleteCompletedFile(video.Path, &taskRecord)
    }
    continue
}
```

### 3. 添加主动清理机制
- **deleteCompletedFile**：删除单个已完成文件
- **cleanupCompletedFiles**：批量清理已完成文件
- **cleanupLoop**：定期清理循环（每小时执行一次）

### 4. 增强监控和通知
- 文件删除时发送WebSocket通知
- 记录释放的磁盘空间统计
- 提供详细的清理日志

## 🎯 预期效果

### 1. 磁盘空间优化
- **自动清理**：已上传文件自动删除，释放磁盘空间
- **空间统计**：实时监控释放的空间大小
- **通知机制**：清理完成后发送通知

### 2. 避免重复下载
- **智能检测**：检查数据库中已完成的下载任务
- **资源节约**：避免重复下载相同影片
- **效率提升**：减少不必要的网络和存储开销

### 3. 系统性能提升
- **定期清理**：每小时自动清理一次
- **即时清理**：发现已完成文件立即清理
- **空目录清理**：自动删除空目录

## 📋 配置建议

### 当前配置检查
```yaml
file_processing:
  auto_upload:
    delete_after_upload: true  # ✅ 已启用
    scan_interval: 5          # 5分钟扫描一次
    max_concurrent_uploads: 1  # 串行上传
```

### 推荐配置优化
```yaml
file_processing:
  auto_upload:
    delete_after_upload: true
    scan_interval: 3          # 建议缩短到3分钟
    max_concurrent_uploads: 2  # 可适当增加并发
    cleanup_interval: 60      # 新增：清理间隔（分钟）
    force_cleanup_on_start: true  # 新增：启动时强制清理
```

## 🔧 使用说明

### 1. 立即生效
修改后的代码会在下次重启服务时生效：
```bash
cd /www/wwwroot/JAVAPI.COM
pkill -f "make scraper"
nohup make scraper > logs/scraper.log 2>&1 &
```

### 2. 监控清理效果
```bash
# 查看清理日志
tail -f logs/scraper.log | grep "清理\|删除\|释放"

# 检查磁盘使用情况
df -h /www/wwwroot/JAVAPI.COM/downloads
```

### 3. 手动触发清理
如需立即清理，可以重启auto_upload服务，它会在启动5分钟后执行第一次清理。

## ⚠️ 注意事项

### 1. 安全保护
- 只删除已确认上传成功且有播放链接的文件
- 保留根目录文件的特殊处理逻辑
- 不会删除downloads根目录本身

### 2. 数据完整性
- 删除前会验证数据库中的播放链接
- 确保文件已成功上传到流媒体平台
- 保留数据库记录，只删除本地文件

### 3. 性能考虑
- 清理任务在后台异步执行
- 不影响正常的上传和下载流程
- 定期清理避免一次性处理大量文件

## 📊 效果预测

基于当前系统状态（UBJ-001等已完成任务），预计：
- **立即释放**：约10-20GB磁盘空间
- **持续优化**：每天节约5-10GB存储空间
- **重复下载减少**：避免90%以上的重复下载任务
- **系统性能提升**：减少磁盘I/O压力，提升整体响应速度