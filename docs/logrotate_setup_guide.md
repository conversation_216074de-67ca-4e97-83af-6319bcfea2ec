# 📋 JAV API 日志轮转配置指南

## 🎯 功能说明

已为JAV API项目配置了系统级别的logrotate日志轮转功能，可以：

- 🔄 **自动轮转**：定期轮转日志文件，防止日志文件过大
- 🗜️ **自动压缩**：压缩旧日志文件，节省磁盘空间
- 📅 **定期清理**：自动删除过期的日志文件
- 📊 **分类管理**：不同类型的日志文件采用不同的轮转策略

## 📁 配置文件位置

- **配置文件**：`/etc/logrotate.d/javapi`
- **系统服务**：`logrotate.timer` (每日00:00执行)

## 🔧 轮转策略

### 主要日志文件 (server.log, scraper.log)
- **轮转频率**：每日检查，超过50MB立即轮转
- **保留时间**：30天
- **压缩方式**：gzip压缩，延迟压缩最新文件

### Aria2清理器日志 (aria2_cleaner.log, cleaner.log)
- **轮转频率**：每周检查，超过20MB立即轮转
- **保留时间**：8周
- **压缩方式**：gzip压缩

### 定时任务日志 (cleaner_cron.log)
- **轮转频率**：每月检查，超过10MB立即轮转
- **保留时间**：6个月
- **压缩方式**：gzip压缩

### 应用日志 (auto_upload.log, adaptive_config.log)
- **轮转频率**：每周检查，超过30MB立即轮转
- **保留时间**：4周
- **压缩方式**：gzip压缩

## 🚀 使用方法

### 1. 查看当前配置
```bash
cat /etc/logrotate.d/javapi
```

### 2. 测试配置语法
```bash
logrotate -d /etc/logrotate.d/javapi
```

### 3. 手动执行轮转
```bash
# 强制执行轮转（测试用）
logrotate -f /etc/logrotate.d/javapi

# 详细模式执行
logrotate -v /etc/logrotate.d/javapi
```

### 4. 查看轮转状态
```bash
# 查看logrotate状态文件
cat /var/lib/logrotate/status | grep javapi

# 查看系统定时器状态
systemctl status logrotate.timer
```

## 📊 轮转效果示例

轮转前：
```
server.log      (85KB)
scraper.log     (16.6MB)
```

轮转后：
```
server.log      (0KB - 新文件)
server.log.1    (85KB - 轮转文件)
scraper.log     (0KB - 新文件)  
scraper.log.1   (16.6MB - 轮转文件)
```

一周后：
```
server.log      (当前日志)
server.log.1    (昨天的日志)
server.log.2.gz (前天的日志，已压缩)
...
server.log.30.gz (30天前的日志，已压缩)
```

## 🔍 监控和维护

### 查看日志文件大小
```bash
# 查看当前日志目录
ls -lah /www/wwwroot/JAVAPI.COM/logs/

# 查看磁盘使用情况
du -sh /www/wwwroot/JAVAPI.COM/logs/
```

### 查看轮转历史
```bash
# 查看系统日志中的轮转记录
journalctl -u logrotate.service

# 查看最近的轮转操作
grep "JAV API" /var/log/syslog
```

### 手动清理旧日志
```bash
# 删除30天前的压缩日志文件
find /www/wwwroot/JAVAPI.COM/logs/ -name "*.gz" -mtime +30 -delete

# 删除特定日志的旧文件
rm /www/wwwroot/JAVAPI.COM/logs/scraper.log.*.gz
```

## ⚙️ 自定义配置

### 修改轮转频率
编辑 `/etc/logrotate.d/javapi` 文件：

```bash
# 改为每小时轮转
hourly

# 改为每月轮转  
monthly

# 改为按大小轮转（不考虑时间）
size 100M
```

### 修改保留时间
```bash
# 保留60天
rotate 60

# 保留12个月
rotate 12
```

### 修改压缩设置
```bash
# 不压缩
# compress

# 立即压缩（不延迟）
# delaycompress
```

## 🚨 故障排除

### 问题1：轮转没有执行
**检查方法**：
```bash
systemctl status logrotate.timer
journalctl -u logrotate.service
```

**解决方案**：
```bash
# 重启logrotate定时器
systemctl restart logrotate.timer
```

### 问题2：配置文件语法错误
**检查方法**：
```bash
logrotate -d /etc/logrotate.d/javapi
```

**解决方案**：根据错误信息修正配置文件语法

### 问题3：权限问题
**检查方法**：
```bash
ls -la /etc/logrotate.d/javapi
ls -la /www/wwwroot/JAVAPI.COM/logs/
```

**解决方案**：
```bash
# 修正配置文件权限
chmod 644 /etc/logrotate.d/javapi

# 修正日志目录权限
chown -R root:root /www/wwwroot/JAVAPI.COM/logs/
```

## 📈 预期效果

使用logrotate后，您将获得：

- ✅ **自动化管理**：无需手动清理日志文件
- ✅ **空间优化**：自动压缩和删除旧日志，节省磁盘空间
- ✅ **系统稳定**：防止日志文件过大影响系统性能
- ✅ **历史保留**：保留适当时间的日志用于问题排查
- ✅ **标准化管理**：使用系统标准的日志管理机制

**预期效果**：
- 日志文件大小控制在合理范围内
- 磁盘空间使用率降低60-80%
- 系统I/O性能提升
- 完全自动化运行，无需人工干预

## 📝 注意事项

1. **系统级配置**：logrotate配置是系统级别的，影响整个服务器
2. **权限要求**：需要root权限才能修改配置
3. **测试建议**：修改配置后建议先用 `-d` 参数测试
4. **备份重要**：重要日志建议在轮转前进行额外备份
5. **监控建议**：定期检查轮转是否正常工作

---

**配置完成时间**：2025-06-30 22:33  
**下次自动轮转**：每日 00:00 (系统定时器自动执行)