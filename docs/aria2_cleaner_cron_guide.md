# ⏰ Aria2智能清理器定时任务配置指南

## 🎯 功能说明

Aria2智能清理器现在支持定时自动运行，可以：
- 🔄 **自动监控**：定时检查慢速下载任务
- 🗑️ **自动清理**：删除慢速任务文件释放空间
- 📝 **保留任务**：保持aria2任务记录完整
- 📊 **定时报告**：记录清理统计和效果

## 🚀 快速配置

### 1. 安装定时任务（推荐10分钟间隔）
```bash
cd /www/wwwroot/JAVAPI.COM
./scripts/setup_cleaner_cron.sh install
```

### 2. 查看运行状态
```bash
./scripts/setup_cleaner_cron.sh status
```

### 3. 查看实时日志
```bash
./scripts/setup_cleaner_cron.sh log
```

## ⏱️ 时间间隔选项

| 间隔 | 说明 | 适用场景 |
|------|------|----------|
| `5min` | 每5分钟 | 高频下载，快速响应 |
| `10min` | 每10分钟 | **推荐设置**，平衡效率和资源 |
| `15min` | 每15分钟 | 中等频率，节省资源 |
| `30min` | 每30分钟 | 低频检查，最小资源占用 |
| `1hour` | 每小时 | 定期维护模式 |
| `2hour` | 每2小时 | 轻度使用场景 |

## 📋 详细命令

### 安装定时任务
```bash
# 使用默认10分钟间隔
./scripts/setup_cleaner_cron.sh install

# 指定5分钟间隔（高频清理）
./scripts/setup_cleaner_cron.sh install 5min

# 指定30分钟间隔（节省资源）
./scripts/setup_cleaner_cron.sh install 30min

# 指定每小时清理一次
./scripts/setup_cleaner_cron.sh install 1hour
```

### 管理定时任务
```bash
# 查看当前状态
./scripts/setup_cleaner_cron.sh status

# 移除定时任务
./scripts/setup_cleaner_cron.sh remove

# 查看实时日志
./scripts/setup_cleaner_cron.sh log
```

### 手动执行
```bash
# 立即执行一次清理
./scripts/start_cleaner.sh once

# 查看磁盘使用情况
./scripts/start_cleaner.sh disk

# 查看已清理的任务
./scripts/start_cleaner.sh list
```

## 📊 状态监控

### 查看定时任务状态
```bash
./scripts/setup_cleaner_cron.sh status
```

**输出示例**：
```
📊 Aria2智能清理器定时任务状态
================================
✅ 定时任务: 已安装
📋 任务详情:
   描述: Aria2智能清理器 - 10min间隔
   表达式: */10 * * * *
   脚本: /www/wwwroot/JAVAPI.COM/scripts/start_cleaner.sh once

📄 日志文件: 存在
   路径: /www/wwwroot/JAVAPI.COM/logs/cleaner_cron.log
   大小: 2.1K
   最后修改: 2025-06-29 10:30:01

📈 最近执行记录 (最后5次):
   清理完成: 清理了 2 个任务，释放 8.5 GB
   清理完成: 清理了 1 个任务，释放 3.2 GB
   清理完成: 清理了 0 个任务，释放 0.0 GB
```

### 查看执行日志
```bash
# 查看实时日志
./scripts/setup_cleaner_cron.sh log

# 查看历史日志
tail -50 /www/wwwroot/JAVAPI.COM/logs/cleaner_cron.log
```

## 🔧 高级配置

### 修改清理参数
编辑清理器脚本中的参数：
```bash
nano /www/wwwroot/JAVAPI.COM/scripts/aria2_smart_cleaner.py
```

**可调整参数**：
```python
# 性能阈值配置
MIN_SPEED_THRESHOLD = 50 * 1024  # 50KB/s 最低速度阈值
SLOW_TASK_TIMEOUT = 300  # 5分钟低速超时
MIN_FILE_SIZE_TO_CLEAN = 100 * 1024 * 1024  # 100MB以上才清理
```

### 自定义cron表达式
如果需要特殊的时间间隔，可以手动编辑crontab：
```bash
crontab -e
```

**cron表达式示例**：
```bash
# 每天凌晨2点执行
0 2 * * * /www/wwwroot/JAVAPI.COM/scripts/start_cleaner.sh once

# 每周日凌晨3点执行
0 3 * * 0 /www/wwwroot/JAVAPI.COM/scripts/start_cleaner.sh once

# 工作日每2小时执行
0 */2 * * 1-5 /www/wwwroot/JAVAPI.COM/scripts/start_cleaner.sh once
```

## 📈 效果监控

### 磁盘空间监控
```bash
# 查看磁盘使用情况
./scripts/start_cleaner.sh disk

# 输出示例：
# 💾 磁盘使用情况:
#   总空间: 502.8 GB
#   已使用: 160.4 GB
#   可用空间: 342.5 GB
#   使用率: 31.9%
```

### 清理效果统计
```bash
# 查看已清理的任务
./scripts/start_cleaner.sh list

# 输出示例：
# 已清理任务 (5 个):
#   - mizd-464 (GID: abc123) - 释放 8.5 GB
#   - ssis-123 (GID: def456) - 释放 4.2 GB
#   - pred-789 (GID: ghi789) - 释放 2.9 GB
```

## ⚠️ 注意事项

### 安全提醒
1. **文件会被删除**：清理操作会真正删除慢速任务的文件
2. **任务可恢复**：aria2任务记录保留，可以重新下载
3. **定期检查**：建议定期查看日志确认清理效果

### 性能影响
1. **资源占用**：每次检查占用很少CPU和内存
2. **网络影响**：仅查询aria2 RPC，无网络下载
3. **磁盘IO**：删除文件时有短暂IO操作

### 最佳实践
1. **推荐间隔**：10分钟间隔适合大多数场景
2. **监控日志**：定期查看执行日志
3. **备份重要文件**：清理前确保重要文件已备份
4. **调整参数**：根据实际情况调整速度阈值

## 🆘 故障排除

### 常见问题

**Q: 定时任务没有执行？**
A: 检查crontab服务是否运行：`systemctl status cron`

**Q: 清理器无法连接aria2？**
A: 检查aria2服务状态和RPC配置

**Q: 日志文件过大？**
A: 可以定期清理日志：`> /www/wwwroot/JAVAPI.COM/logs/cleaner_cron.log`

**Q: 想要停止定时清理？**
A: 运行移除命令：`./scripts/setup_cleaner_cron.sh remove`

### 调试命令
```bash
# 检查crontab任务
crontab -l

# 手动测试清理器
./scripts/start_cleaner.sh once

# 查看系统日志
tail -f /var/log/syslog | grep CRON

# 检查脚本权限
ls -la /www/wwwroot/JAVAPI.COM/scripts/
```

## 🎉 预期效果

使用定时清理后，您将获得：

- ✅ **自动化管理**：无需手动干预
- ✅ **空间优化**：自动释放慢速任务占用的空间
- ✅ **下载效率**：专注于高速下载任务
- ✅ **任务保留**：所有任务记录完整保存
- ✅ **详细日志**：完整的操作记录和统计

**预期效果**：硬盘使用率降低20-50%，下载效率提升30-80%，完全自动化运行！