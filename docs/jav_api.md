# JAV API 文档

## 概述

JAV API 提供了完整的日本成人影片数据管理功能，包括影片信息查询、演员管理、分类浏览、磁力链接获取、数据采集和下载管理等功能。

## 基础信息

- **Base URL**: `http://localhost:8080/api/v1/jav`
- **认证方式**: <PERSON><PERSON> (JWT)
- **响应格式**: JSON

## 通用响应格式

```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 影片相关API

### 1. 获取最新影片

**GET** `/movies/latest`

获取最新发布的影片列表（公开接口）

**参数:**
- `limit` (query, optional): 返回数量，默认20，最大100

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "code": "DEMO-001",
      "title": "演示影片1",
      "title_en": "Demo Movie 1",
      "studio": "演示工作室",
      "release_date": "2024-01-15T00:00:00Z",
      "duration": 120,
      "rating": 8.5,
      "cover_url": "https://example.com/cover1.jpg",
      "poster_url": "https://example.com/poster1.jpg",
      "scraping_status": "completed",
      "scraping_source": "javbus",
      "actor_count": 2,
      "genre_count": 3,
      "magnet_count": 4,
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T10:00:00Z"
    }
  ]
}
```

### 2. 获取热门影片

**GET** `/movies/popular`

获取热门影片列表，按评分和观看次数排序（公开接口）

**参数:**
- `limit` (query, optional): 返回数量，默认20，最大100

### 3. 搜索影片

**GET** `/movies/search` 🔒

根据关键词、工作室、演员等条件搜索影片

**参数:**
- `keyword` (query, optional): 搜索关键词
- `studio` (query, optional): 工作室名称
- `actor_name` (query, optional): 演员姓名
- `genre_name` (query, optional): 分类名称
- `year` (query, optional): 发布年份
- `rating_min` (query, optional): 最低评分
- `rating_max` (query, optional): 最高评分
- `has_subtitle` (query, optional): 是否有字幕
- `sort_by` (query, optional): 排序字段 (created_at, release_date, rating, duration)
- `sort_desc` (query, optional): 是否降序排列
- `page` (query, optional): 页码，默认1
- `page_size` (query, optional): 每页数量，默认20，最大100

**响应示例:**
```json
{
  "success": true,
  "data": {
    "movies": [...],
    "total": 150,
    "page": 1,
    "page_size": 20,
    "has_more": true
  }
}
```

### 4. 获取影片详情

**GET** `/movies/{code}` 🔒

根据影片番号获取详细信息，包括演员、分类、磁力链接等

**路径参数:**
- `code`: 影片番号

**响应示例:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "code": "DEMO-001",
    "title": "演示影片1",
    "title_en": "Demo Movie 1",
    "studio": "演示工作室",
    "release_date": "2024-01-15T00:00:00Z",
    "duration": 120,
    "rating": 8.5,
    "plot": "影片简介",
    "plot_en": "Movie plot",
    "cover_url": "https://example.com/cover1.jpg",
    "poster_url": "https://example.com/poster1.jpg",
    "actors": [
      {
        "id": 1,
        "name": "演员A",
        "name_en": "Actor A",
        "avatar_url": "https://example.com/actor1.jpg"
      }
    ],
    "genres": [
      {
        "id": 1,
        "name": "分类A",
        "name_en": "Genre A"
      }
    ],
    "magnets": [
      {
        "id": 1,
        "magnet_url": "magnet:?xt=urn:btih:...",
        "file_name": "DEMO-001.1080p.mp4",
        "file_size": 3221225472,
        "file_size_formatted": "3.0 GB",
        "quality": "1080p",
        "has_subtitle": true,
        "subtitle_language": "chinese",
        "score": 95.0
      }
    ]
  }
}
```

### 5. 获取影片磁力链接

**GET** `/movies/{code}/magnets` 🔒

获取指定影片的所有磁力链接，按评分排序

### 6. 获取最佳磁力链接

**GET** `/movies/{code}/best-magnet` 🔒

获取指定影片的最佳磁力链接（评分最高）

## 演员相关API

### 1. 获取热门演员

**GET** `/actors/popular`

获取热门演员列表，按影片数量排序（公开接口）

### 2. 搜索演员

**GET** `/actors/search` 🔒

根据姓名、身高、血型等条件搜索演员

**参数:**
- `keyword` (query, optional): 搜索关键词
- `height_min` (query, optional): 最低身高
- `height_max` (query, optional): 最高身高
- `blood_type` (query, optional): 血型
- `debut_year` (query, optional): 出道年份
- `sort_by` (query, optional): 排序字段 (name, height, debut_date)
- `sort_desc` (query, optional): 是否降序排列
- `page` (query, optional): 页码，默认1
- `page_size` (query, optional): 每页数量，默认20，最大100

### 3. 获取演员详情

**GET** `/actors/{id}` 🔒

根据演员ID获取详细信息

### 4. 获取演员影片

**GET** `/actors/{id}/movies` 🔒

获取指定演员的影片列表

## 分类相关API

### 1. 获取所有分类

**GET** `/genres`

获取所有影片分类列表（公开接口）

### 2. 获取热门分类

**GET** `/genres/popular`

获取热门分类列表，按影片数量排序（公开接口）

### 3. 获取分类影片

**GET** `/genres/{id}/movies` 🔒

获取指定分类下的影片列表

## 磁力链接相关API

### 1. 根据清晰度获取磁力链接

**GET** `/magnets/quality/{quality}` 🔒

获取指定清晰度的磁力链接列表

**路径参数:**
- `quality`: 清晰度 (720p, 1080p, 4K)

## 数据采集相关API

### 1. 触发数据采集

**POST** `/scraping/trigger` 🔒👑

手动触发指定影片的数据采集（需要管理员权限）

**请求体:**
```json
{
  "code": "NEW-001"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "movie_info": {...},
    "source": "javbus",
    "duration": "2.5s",
    "confidence": 0.95,
    "actors_found": 2,
    "genres_found": 3,
    "magnets_found": 5
  }
}
```

### 2. 批量数据采集

**POST** `/scraping/batch` 🔒👑

批量采集多个影片的数据（需要管理员权限）

**请求体:**
```json
{
  "codes": ["BATCH-001", "BATCH-002", "BATCH-003"]
}
```

### 3. 获取采集统计

**GET** `/scraping/stats` 🔒👑

获取数据采集的统计信息（需要管理员权限）

## 下载相关API

### 1. 创建下载任务

**POST** `/downloads` 🔒

为指定影片创建下载任务

**请求体:**
```json
{
  "movie_code": "DEMO-001",
  "magnet_id": 1,
  "options": {
    "quality": "1080p",
    "prefer_subtitle": true,
    "subtitle_language": "chinese",
    "auto_upload": false,
    "priority": 3
  }
}
```

### 2. 获取下载任务列表

**GET** `/downloads` 🔒

获取用户的JAV下载任务列表

**参数:**
- `status` (query, optional): 任务状态
- `movie_code` (query, optional): 影片番号
- `page` (query, optional): 页码，默认1
- `page_size` (query, optional): 每页数量，默认20，最大100

## 统计相关API

### 1. 获取JAV统计信息

**GET** `/stats`

获取影片、演员、磁力链接等统计信息（公开接口）

**响应示例:**
```json
{
  "success": true,
  "data": {
    "total_movies": 1500,
    "total_actors": 800,
    "total_genres": 50,
    "total_magnets": 5000,
    "completed_movies": 1200,
    "pending_movies": 200,
    "failed_movies": 100,
    "avg_rating": 7.8,
    "avg_magnets_per_movie": 3.3
  }
}
```

## 错误码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源未找到 |
| 500 | 服务器内部错误 |

## 图标说明

- 🔒 需要用户认证
- 👑 需要管理员权限

## 使用示例

### 获取最新影片

```bash
curl -X GET "http://localhost:8080/api/v1/jav/movies/latest?limit=5"
```

### 搜索影片

```bash
curl -X GET "http://localhost:8080/api/v1/jav/movies/search?keyword=演示&page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 获取影片详情

```bash
curl -X GET "http://localhost:8080/api/v1/jav/movies/DEMO-001" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 触发数据采集

```bash
curl -X POST "http://localhost:8080/api/v1/jav/scraping/trigger" \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"code": "NEW-001"}'
```

### 创建下载任务

```bash
curl -X POST "http://localhost:8080/api/v1/jav/downloads" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "movie_code": "DEMO-001",
    "options": {
      "quality": "1080p",
      "prefer_subtitle": true,
      "auto_upload": false,
      "priority": 3
    }
  }'
```

## 注意事项

1. 所有需要认证的接口都需要在请求头中包含有效的JWT Token
2. 管理员权限接口只有管理员用户才能访问
3. 分页查询的页码从1开始
4. 影片番号会自动转换为大写格式
5. 文件大小会自动格式化为人类可读的格式
6. 所有时间字段都使用ISO 8601格式