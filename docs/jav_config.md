# JAV功能配置文档

## 概述

JAV功能配置系统提供了完整的日本成人影片数据管理功能配置，包括数据采集、下载管理、存储处理、API控制等多个方面的详细配置选项。

## 配置文件结构

JAV配置位于主配置文件的`jav`节点下，支持YAML格式配置：

```yaml
jav:
  enabled: true  # 是否启用JAV功能
  scraping: {...}    # 数据采集配置
  download: {...}    # 下载配置
  storage: {...}     # 存储配置
  processing: {...}  # 处理配置
  api: {...}         # API配置
```

## 详细配置说明

### 1. 主配置 (jav)

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `enabled` | bool | `false` | 是否启用JAV功能 |

### 2. 数据采集配置 (jav.scraping)

#### 基础配置

| 配置项 | 类型 | 默认值 | 说明 | 范围 |
|--------|------|--------|------|------|
| `enabled` | bool | `true` | 是否启用数据采集 | - |
| `timeout` | int | `30` | 请求超时时间(秒) | 5-300 |
| `rate_limit` | int | `2` | 请求间隔(秒) | 0-60 |
| `max_retries` | int | `3` | 最大重试次数 | 0-10 |
| `auto_merge` | bool | `true` | 是否自动融合多源数据 | - |
| `min_confidence` | float | `0.7` | 最小可信度 | 0.0-1.0 |
| `batch_size` | int | `10` | 批量处理大小 | 1-100 |
| `concurrent_limit` | int | `5` | 并发限制 | 1-20 |
| `user_agent` | string | Mozilla... | 用户代理字符串 | - |
| `proxy_enabled` | bool | `false` | 是否启用代理 | - |
| `proxy_url` | string | `""` | 代理URL | - |

#### 数据源配置 (jav.scraping.sources)

支持多个数据源，每个数据源都有独立的配置：

**JavBus配置 (jav.scraping.sources.javbus)**

| 配置项 | 类型 | 默认值 | 说明 | 范围 |
|--------|------|--------|------|------|
| `enabled` | bool | `true` | 是否启用JavBus | - |
| `base_url` | string | `https://www.javbus.com` | 基础URL | - |
| `timeout` | int | `30` | 超时时间(秒) | 5-300 |
| `max_retries` | int | `3` | 最大重试次数 | 0-10 |
| `priority` | int | `9` | 优先级 | 1-10 |
| `weight` | float | `0.4` | 权重 | 0.0-1.0 |
| `rate_limit` | int | `2` | 速率限制(秒) | 0-60 |
| `user_agent` | string | Mozilla... | 用户代理 | - |
| `headers` | map | `{}` | 自定义请求头 | - |

**其他数据源**
- `javinizer`: Javinizer数据源配置
- `javsp`: JavSP数据源配置  
- `javdb`: JavDB数据源配置
- `javlibrary`: JavLibrary数据源配置

每个数据源的配置项与JavBus相同。

### 3. 下载配置 (jav.download)

| 配置项 | 类型 | 默认值 | 说明 | 可选值 |
|--------|------|--------|------|--------|
| `enabled` | bool | `true` | 是否启用JAV下载 | - |
| `default_path` | string | `/downloads/jav` | 默认下载路径 | - |
| `create_folder` | bool | `true` | 是否为每部影片创建文件夹 | - |
| `folder_template` | string | `{studio}/{code} - {title}` | 文件夹命名模板 | - |
| `file_template` | string | `{code} - {title}.{ext}` | 文件命名模板 | - |
| `prefer_quality` | string | `1080p` | 首选清晰度 | 720p, 1080p, 4K, auto |
| `prefer_subtitle` | bool | `true` | 是否优先选择有字幕的 | - |
| `subtitle_lang` | string | `chinese` | 首选字幕语言 | chinese, english, japanese, auto, none |
| `auto_start` | bool | `false` | 是否自动开始下载 | - |
| `max_concurrent` | int | `3` | 最大并发下载数 | 1-20 |
| `speed_limit` | int64 | `0` | 速度限制(KB/s, 0为不限制) | ≥0 |
| `auto_upload` | bool | `false` | 是否自动上传 | - |
| `upload_platform` | string | `streamtape` | 上传平台 | - |
| `clean_after_upload` | bool | `false` | 上传后是否清理本地文件 | - |

#### 命名模板变量

可在`folder_template`和`file_template`中使用的变量：

- `{code}`: 影片番号
- `{title}`: 影片标题
- `{studio}`: 工作室名称
- `{year}`: 发布年份
- `{month}`: 发布月份
- `{day}`: 发布日期
- `{actors}`: 演员名称(逗号分隔)
- `{genres}`: 分类名称(逗号分隔)
- `{ext}`: 文件扩展名

### 4. 存储配置 (jav.storage)

| 配置项 | 类型 | 默认值 | 说明 | 范围 |
|--------|------|--------|------|------|
| `cover_path` | string | `/storage/jav/covers` | 封面存储路径 | - |
| `poster_path` | string | `/storage/jav/posters` | 海报存储路径 | - |
| `actor_avatar_path` | string | `/storage/jav/actors` | 演员头像存储路径 | - |
| `max_cover_size` | int64 | `5242880` | 最大封面文件大小(5MB) | ≥0 |
| `max_poster_size` | int64 | `10485760` | 最大海报文件大小(10MB) | ≥0 |
| `image_quality` | int | `85` | 图片质量 | 1-100 |
| `image_format` | string | `jpg` | 图片格式 | jpg, jpeg, png, webp |
| `enable_webp` | bool | `true` | 是否启用WebP格式 | - |
| `enable_thumbnail` | bool | `true` | 是否生成缩略图 | - |
| `thumbnail_size` | int | `300` | 缩略图大小(像素) | 50-1000 |

### 5. 处理配置 (jav.processing)

| 配置项 | 类型 | 默认值 | 说明 | 范围 |
|--------|------|--------|------|------|
| `enable_metadata` | bool | `true` | 是否启用元数据处理 | - |
| `enable_thumbnail` | bool | `true` | 是否生成缩略图 | - |
| `enable_preview` | bool | `false` | 是否生成预览 | - |
| `thumbnail_count` | int | `10` | 缩略图数量 | 0-50 |
| `thumbnail_width` | int | `320` | 缩略图宽度 | 100-1920 |
| `thumbnail_height` | int | `180` | 缩略图高度 | 100-1080 |
| `preview_duration` | int | `30` | 预览时长(秒) | 10-300 |
| `preview_quality` | string | `medium` | 预览质量 | low, medium, high |
| `ffmpeg_path` | string | `/usr/bin/ffmpeg` | FFmpeg路径 | - |
| `ffprobe_path` | string | `/usr/bin/ffprobe` | FFprobe路径 | - |
| `temp_path` | string | `/tmp/jav_processing` | 临时文件路径 | - |
| `clean_temp_files` | bool | `true` | 是否清理临时文件 | - |

### 6. API配置 (jav.api)

| 配置项 | 类型 | 默认值 | 说明 | 范围 |
|--------|------|--------|------|------|
| `enable_public_api` | bool | `true` | 是否启用公开API | - |
| `enable_search` | bool | `true` | 是否启用搜索功能 | - |
| `enable_download` | bool | `true` | 是否启用下载功能 | - |
| `enable_scraping` | bool | `false` | 是否启用采集功能(仅管理员) | - |
| `max_search_results` | int | `100` | 最大搜索结果数 | 10-1000 |
| `max_page_size` | int | `50` | 最大分页大小 | 10-200 |
| `cache_enabled` | bool | `true` | 是否启用缓存 | - |
| `cache_ttl` | int | `3600` | 缓存TTL(秒) | 60-86400 |
| `rate_limit_enabled` | bool | `true` | 是否启用速率限制 | - |
| `rate_limit_rpm` | int | `1000` | 每分钟请求限制 | 10-10000 |
| `allowed_origins` | []string | `[]` | 允许的跨域来源 | - |
| `require_auth` | bool | `false` | 是否需要认证 | - |
| `admin_only` | bool | `false` | 是否仅管理员可用 | - |

## 环境变量覆盖

所有配置项都可以通过环境变量覆盖，格式为：`MD_` + 配置路径（用下划线分隔）

### 示例

```bash
# 启用JAV功能
export MD_JAV_ENABLED=true

# 设置采集超时时间
export MD_JAV_SCRAPING_TIMEOUT=45

# 设置首选视频质量
export MD_JAV_DOWNLOAD_PREFER_QUALITY=4K

# 设置最大搜索结果数
export MD_JAV_API_MAX_SEARCH_RESULTS=200

# 启用JavBus数据源
export MD_JAV_SCRAPING_SOURCES_JAVBUS_ENABLED=true

# 设置JavBus优先级
export MD_JAV_SCRAPING_SOURCES_JAVBUS_PRIORITY=10
```

## 配置验证

系统会自动验证配置的有效性，包括：

### 数据采集配置验证
- 超时时间必须在5-300秒之间
- 速率限制必须在0-60秒之间
- 最大重试次数必须在0-10之间
- 最小可信度必须在0.0-1.0之间
- 批量大小必须在1-100之间
- 并发限制必须在1-20之间

### 数据源配置验证
- 启用的数据源必须提供base_url
- 优先级必须在1-10之间
- 权重必须在0.0-1.0之间

### 下载配置验证
- 启用下载时必须提供default_path
- 最大并发数必须在1-20之间
- 速度限制必须非负
- 首选质量必须是有效值
- 字幕语言必须是有效值

### 存储配置验证
- 文件大小限制必须非负
- 图片质量必须在1-100之间
- 图片格式必须是支持的格式
- 缩略图大小必须在50-1000像素之间

### 处理配置验证
- 缩略图数量必须在0-50之间
- 缩略图尺寸必须在合理范围内
- 预览时长必须在10-300秒之间
- 预览质量必须是有效值

### API配置验证
- 最大搜索结果数必须在10-1000之间
- 最大分页大小必须在10-200之间
- 缓存TTL必须在60-86400秒之间
- 速率限制必须在10-10000之间

## 配置热重载

系统支持配置热重载，无需重启服务即可应用新的配置：

```go
// 监听配置文件变化
config.WatchConfig(func(newConfig *config.Config) {
    // 处理配置更新
    fmt.Printf("配置已更新: JAV启用状态 = %v\n", newConfig.JAV.Enabled)
})

// 手动重载配置
newConfig, err := config.Reload()
if err != nil {
    log.Printf("重载配置失败: %v", err)
} else {
    log.Println("配置重载成功")
}
```

## 最佳实践

### 1. 生产环境配置

```yaml
jav:
  enabled: true
  scraping:
    enabled: true
    timeout: 30
    rate_limit: 3  # 生产环境建议更保守的速率限制
    max_retries: 2
    concurrent_limit: 3  # 减少并发以避免被封
    sources:
      javbus:
        enabled: true
        priority: 9
        weight: 0.5
        rate_limit: 3
      javinizer:
        enabled: true
        priority: 8
        weight: 0.3
        rate_limit: 5
  download:
    enabled: true
    max_concurrent: 2  # 生产环境减少并发
    speed_limit: 5120  # 限制速度为5MB/s
  api:
    cache_enabled: true
    cache_ttl: 7200  # 增加缓存时间
    rate_limit_enabled: true
    rate_limit_rpm: 500  # 生产环境更严格的限制
```

### 2. 开发环境配置

```yaml
jav:
  enabled: true
  scraping:
    enabled: true
    timeout: 15  # 开发环境更短的超时
    rate_limit: 1
    max_retries: 1
    concurrent_limit: 2
  download:
    enabled: false  # 开发环境禁用下载
  api:
    cache_enabled: false  # 开发环境禁用缓存
    rate_limit_enabled: false  # 开发环境禁用速率限制
```

### 3. 安全配置建议

- 生产环境中设置合理的速率限制，避免被数据源网站封禁
- 启用代理以保护服务器IP
- 设置合理的并发限制，避免对目标网站造成过大压力
- 定期更新User-Agent字符串
- 监控采集成功率，及时调整配置

### 4. 性能优化建议

- 根据服务器性能调整并发限制
- 启用缓存以减少重复请求
- 设置合理的超时时间
- 定期清理临时文件和缓存
- 监控内存和磁盘使用情况

## 故障排除

### 常见问题

1. **配置验证失败**
   - 检查配置值是否在有效范围内
   - 确认必需的配置项已设置
   - 查看详细的错误信息

2. **数据采集失败**
   - 检查网络连接
   - 验证数据源URL是否可访问
   - 调整超时时间和重试次数
   - 检查User-Agent是否被封禁

3. **下载失败**
   - 确认下载路径存在且可写
   - 检查磁力链接是否有效
   - 验证Aria2配置是否正确

4. **API响应慢**
   - 启用缓存
   - 调整数据库连接池大小
   - 优化查询条件
   - 增加服务器资源

### 日志配置

建议在生产环境中启用详细的日志记录：

```yaml
log:
  level: "info"
  format: "json"
  output: "file"
  file_path: "/var/log/jav-api.log"
```

### 监控指标

建议监控以下指标：

- 采集成功率
- API响应时间
- 缓存命中率
- 下载成功率
- 错误率
- 资源使用情况

## 配置示例

完整的生产环境配置示例请参考 `config.example.yaml` 文件。