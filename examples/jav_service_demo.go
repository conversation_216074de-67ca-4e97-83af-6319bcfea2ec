package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/javscraper"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	fmt.Println("=== JAV服务层演示程序 ===\n")

	// 初始化数据库
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建Repository和Services
	repo := repository.NewRepository(db)
	
	// 创建JAV服务
	javService := service.NewJAVService(repo)
	
	// 创建JAV爬虫服务
	javScraperConfig := &javscraper.Config{
		Timeout:    30 * time.Second,
		RateLimit:  1 * time.Second,
		MaxRetries: 3,
	}
	javScraperService := service.NewJAVScraperService(repo, javScraperConfig)

	// 初始化测试数据
	err = initTestData(repo)
	if err != nil {
		log.Fatalf("初始化测试数据失败: %v", err)
	}

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示JAV服务功能
	demonstrateJAVService(javService)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示JAV爬虫服务功能
	demonstrateJAVScraperService(javScraperService)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示统计功能
	demonstrateStatistics(javService)
}

func initDatabase() (*gorm.DB, error) {
	// 使用内存SQLite数据库进行演示
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	err = db.AutoMigrate(
		&model.JAVMovie{},
		&model.JAVActor{},
		&model.JAVMagnet{},
		&model.JAVGenre{},
		&model.DownloadTask{},
		&model.User{},
	)
	if err != nil {
		return nil, err
	}

	fmt.Println("✅ 数据库初始化完成")
	return db, nil
}

func initTestData(repo repository.Repository) error {
	fmt.Println("🔄 初始化测试数据...")

	// 创建测试用户
	user := &model.User{
		Username: "demo_user",
		Email:    "<EMAIL>",
	}
	err := repo.User().Create(user)
	if err != nil {
		return fmt.Errorf("创建用户失败: %w", err)
	}

	// 创建测试影片
	releaseDate1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	releaseDate2 := time.Date(2024, 2, 20, 0, 0, 0, 0, time.UTC)
	releaseDate3 := time.Date(2024, 3, 10, 0, 0, 0, 0, time.UTC)

	movies := []*model.JAVMovie{
		{
			Code:           "DEMO-001",
			Title:          "演示影片1：青春校园",
			TitleEn:        "Demo Movie 1: School Days",
			Studio:         "演示工作室A",
			ReleaseDate:    &releaseDate1,
			Duration:       120,
			Rating:         8.5,
			Plot:           "一部关于青春校园生活的影片",
			PlotEn:         "A movie about school life",
			CoverURL:       "https://example.com/cover1.jpg",
			PosterURL:      "https://example.com/poster1.jpg",
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavBus,
		},
		{
			Code:           "DEMO-002",
			Title:          "演示影片2：都市恋情",
			TitleEn:        "Demo Movie 2: City Romance",
			Studio:         "演示工作室B",
			ReleaseDate:    &releaseDate2,
			Duration:       95,
			Rating:         7.8,
			Plot:           "一部关于都市恋情的影片",
			PlotEn:         "A movie about city romance",
			CoverURL:       "https://example.com/cover2.jpg",
			PosterURL:      "https://example.com/poster2.jpg",
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavinizer,
		},
		{
			Code:           "DEMO-003",
			Title:          "演示影片3：职场风云",
			TitleEn:        "Demo Movie 3: Office Drama",
			Studio:         "演示工作室A",
			ReleaseDate:    &releaseDate3,
			Duration:       110,
			Rating:         9.2,
			Plot:           "一部关于职场生活的影片",
			PlotEn:         "A movie about office life",
			CoverURL:       "https://example.com/cover3.jpg",
			PosterURL:      "https://example.com/poster3.jpg",
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavBus,
		},
	}

	for _, movie := range movies {
		err := repo.JAVMovie().Create(movie)
		if err != nil {
			return fmt.Errorf("创建影片失败: %w", err)
		}
		fmt.Printf("   ✅ 创建影片: %s - %s\n", movie.Code, movie.Title)
	}

	// 创建测试演员
	debutDate1 := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	debutDate2 := time.Date(2019, 6, 15, 0, 0, 0, 0, time.UTC)

	actors := []*model.JAVActor{
		{
			Name:      "演示演员A",
			NameEn:    "Demo Actor A",
			NameJp:    "デモ俳優A",
			Height:    165,
			Bust:      88,
			Waist:     58,
			Hip:       85,
			BloodType: "A",
			Hobby:     "读书、音乐",
			DebutDate: &debutDate1,
		},
		{
			Name:      "演示演员B",
			NameEn:    "Demo Actor B",
			NameJp:    "デモ俳優B",
			Height:    160,
			Bust:      85,
			Waist:     55,
			Hip:       82,
			BloodType: "B",
			Hobby:     "旅行、摄影",
			DebutDate: &debutDate2,
		},
	}

	for _, actor := range actors {
		err := repo.JAVActor().Create(actor)
		if err != nil {
			return fmt.Errorf("创建演员失败: %w", err)
		}
		fmt.Printf("   ✅ 创建演员: %s (%s)\n", actor.Name, actor.NameEn)
	}

	// 创建测试分类
	genres := []*model.JAVGenre{
		{
			Name:        "校园",
			NameEn:      "School",
			NameJp:      "学校",
			Description: "校园题材影片",
		},
		{
			Name:        "都市",
			NameEn:      "Urban",
			NameJp:      "都市",
			Description: "都市题材影片",
		},
		{
			Name:        "职场",
			NameEn:      "Office",
			NameJp:      "オフィス",
			Description: "职场题材影片",
		},
	}

	for _, genre := range genres {
		err := repo.JAVGenre().Create(genre)
		if err != nil {
			return fmt.Errorf("创建分类失败: %w", err)
		}
		fmt.Printf("   ✅ 创建分类: %s (%s)\n", genre.Name, genre.NameEn)
	}

	// 创建测试磁力链接
	magnets := []*model.JAVMagnet{
		{
			MovieID:          movies[0].ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo001",
			FileName:         "DEMO-001.720p.中文字幕.mp4",
			FileSize:         1610612736, // 1.5GB
			Quality:          model.JAVMagnetQuality720p,
			HasSubtitle:      true,
			SubtitleLanguage: model.JAVMagnetSubtitleLanguageChinese,
			Source:           "javbus",
			Uploader:         "demo_uploader",
			Seeders:          25,
			Leechers:         5,
			Score:            75.0,
		},
		{
			MovieID:          movies[0].ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo001hd",
			FileName:         "DEMO-001.1080p.中文字幕.mp4",
			FileSize:         3221225472, // 3GB
			Quality:          model.JAVMagnetQuality1080p,
			HasSubtitle:      true,
			SubtitleLanguage: model.JAVMagnetSubtitleLanguageChinese,
			Source:           "javbus",
			Uploader:         "hd_uploader",
			Seeders:          50,
			Leechers:         10,
			Score:            95.0,
		},
		{
			MovieID:          movies[1].ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo002",
			FileName:         "DEMO-002.1080p.mp4",
			FileSize:         2684354560, // 2.5GB
			Quality:          model.JAVMagnetQuality1080p,
			HasSubtitle:      false,
			SubtitleLanguage: model.JAVMagnetSubtitleLanguageNone,
			Source:           "javinizer",
			Uploader:         "javinizer_bot",
			Seeders:          30,
			Leechers:         8,
			Score:            80.0,
		},
		{
			MovieID:          movies[2].ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo003",
			FileName:         "DEMO-003.4K.中文字幕[FHD].mp4",
			FileSize:         5368709120, // 5GB
			Quality:          model.JAVMagnetQuality4K,
			HasSubtitle:      true,
			SubtitleLanguage: model.JAVMagnetSubtitleLanguageChinese,
			Source:           "FHD",
			Uploader:         "fhd_uploader",
			Seeders:          100,
			Leechers:         20,
			Score:            100.0,
		},
	}

	for _, magnet := range magnets {
		err := repo.JAVMagnet().Create(magnet)
		if err != nil {
			return fmt.Errorf("创建磁力链接失败: %w", err)
		}
		fmt.Printf("   ✅ 创建磁力链接: %s (评分: %.1f)\n", magnet.FileName, magnet.Score)
	}

	fmt.Println("✅ 测试数据初始化完成")
	return nil
}

func demonstrateJAVService(javService service.JAVService) {
	fmt.Println("🎬 JAV服务功能演示")
	fmt.Println(strings.Repeat("-", 30))

	// 1. 根据番号获取影片
	fmt.Println("📖 1. 根据番号获取影片")
	movie, err := javService.GetMovieByCode("DEMO-001")
	if err != nil {
		log.Printf("获取影片失败: %v", err)
	} else {
		fmt.Printf("   影片: %s - %s\n", movie.Code, movie.Title)
		fmt.Printf("   工作室: %s, 评分: %.1f\n", movie.Studio, movie.Rating)
		fmt.Printf("   时长: %d分钟, 发布日期: %s\n", movie.Duration, 
			movie.ReleaseDate.Format("2006-01-02"))
	}

	// 2. 搜索影片
	fmt.Println("\n🔍 2. 搜索影片")
	searchReq := &service.SearchMoviesRequest{
		Keyword:  "演示",
		Page:     1,
		PageSize: 10,
	}
	searchResult, err := javService.SearchMovies(searchReq)
	if err != nil {
		log.Printf("搜索影片失败: %v", err)
	} else {
		fmt.Printf("   搜索结果: 找到%d部影片\n", searchResult.Total)
		for _, movie := range searchResult.Movies {
			fmt.Printf("   - %s: %s (评分: %.1f)\n", movie.Code, movie.Title, movie.Rating)
		}
	}

	// 3. 获取最新影片
	fmt.Println("\n📅 3. 获取最新影片")
	latestMovies, err := javService.GetLatestMovies(3)
	if err != nil {
		log.Printf("获取最新影片失败: %v", err)
	} else {
		fmt.Printf("   最新影片 (共%d部):\n", len(latestMovies))
		for i, movie := range latestMovies {
			fmt.Printf("   %d. %s: %s\n", i+1, movie.Code, movie.Title)
		}
	}

	// 4. 获取热门影片
	fmt.Println("\n🔥 4. 获取热门影片")
	popularMovies, err := javService.GetPopularMovies(3)
	if err != nil {
		log.Printf("获取热门影片失败: %v", err)
	} else {
		fmt.Printf("   热门影片 (共%d部):\n", len(popularMovies))
		for i, movie := range popularMovies {
			fmt.Printf("   %d. %s: %s (评分: %.1f)\n", i+1, movie.Code, movie.Title, movie.Rating)
		}
	}

	// 5. 获取影片详情
	fmt.Println("\n📋 5. 获取影片详情")
	movieDetails, err := javService.GetMovieDetails(1) // 使用第一部影片的ID
	if err != nil {
		log.Printf("获取影片详情失败: %v", err)
	} else {
		fmt.Printf("   影片详情: %s - %s\n", movieDetails.Code, movieDetails.Title)
		fmt.Printf("   演员数量: %d, 分类数量: %d, 磁力链接数量: %d\n", 
			len(movieDetails.Actors), len(movieDetails.Genres), len(movieDetails.Magnets))
		
		if len(movieDetails.Magnets) > 0 {
			fmt.Printf("   磁力链接:\n")
			for _, magnet := range movieDetails.Magnets {
				fmt.Printf("     - %s: %s, 评分%.1f\n", 
					magnet.Quality, formatBytes(magnet.FileSize), magnet.Score)
			}
		}
	}

	// 6. 获取最佳磁力链接
	fmt.Println("\n🏆 6. 获取最佳磁力链接")
	bestMagnet, err := javService.GetBestMagnet(1) // 使用第一部影片的ID
	if err != nil {
		log.Printf("获取最佳磁力链接失败: %v", err)
	} else {
		fmt.Printf("   最佳磁力链接: %s\n", bestMagnet.FileName)
		fmt.Printf("   清晰度: %s, 文件大小: %s, 评分: %.1f\n", 
			bestMagnet.Quality, bestMagnet.FileSizeFormatted, bestMagnet.Score)
		fmt.Printf("   字幕: %v, 做种数: %d\n", bestMagnet.HasSubtitle, bestMagnet.Seeders)
	}

	// 7. 获取所有分类
	fmt.Println("\n🏷️ 7. 获取所有分类")
	allGenres, err := javService.GetAllGenres()
	if err != nil {
		log.Printf("获取分类失败: %v", err)
	} else {
		fmt.Printf("   分类列表 (共%d个):\n", len(allGenres))
		for _, genre := range allGenres {
			fmt.Printf("   - %s (%s): %s\n", genre.Name, genre.NameEn, genre.Description)
		}
	}

	// 8. 根据清晰度获取磁力链接
	fmt.Println("\n📺 8. 根据清晰度获取磁力链接")
	hdMagnets, total, err := javService.GetMagnetsByQuality("1080p", 0, 10)
	if err != nil {
		log.Printf("获取1080p磁力链接失败: %v", err)
	} else {
		fmt.Printf("   1080p磁力链接 (共%d个):\n", total)
		for _, magnet := range hdMagnets {
			fmt.Printf("   - %s: %s, 评分%.1f\n", 
				magnet.FileName, magnet.FileSizeFormatted, magnet.Score)
		}
	}
}

func demonstrateJAVScraperService(javScraperService service.JAVScraperService) {
	fmt.Println("🕷️ JAV爬虫服务功能演示")
	fmt.Println(strings.Repeat("-", 30))

	// 1. 单个影片采集
	fmt.Println("📥 1. 单个影片采集")
	scrapeResult, err := javScraperService.ScrapeMovieByCode("TEST-001")
	if err != nil {
		log.Printf("采集影片失败: %v", err)
	} else {
		fmt.Printf("   采集结果: %s\n", map[bool]string{true: "成功", false: "失败"}[scrapeResult.Success])
		fmt.Printf("   数据源: %s, 耗时: %v\n", scrapeResult.Source, scrapeResult.Duration)
		fmt.Printf("   可信度: %.2f, 演员数: %d, 分类数: %d, 磁力链接数: %d\n", 
			scrapeResult.Confidence, scrapeResult.ActorsFound, scrapeResult.GenresFound, scrapeResult.MagnetsFound)
	}

	// 2. 多源采集
	fmt.Println("\n🌐 2. 多源采集")
	multiResult, err := javScraperService.ScrapeMovieFromAllSources("TEST-002")
	if err != nil {
		log.Printf("多源采集失败: %v", err)
	} else {
		fmt.Printf("   多源采集结果: 总数据源%d, 成功%d\n", 
			multiResult.TotalSources, multiResult.SuccessSources)
		fmt.Printf("   最佳数据源: %s, 融合成功: %v\n", 
			multiResult.BestSource, multiResult.MergeSuccess)
		fmt.Printf("   总耗时: %v\n", multiResult.TotalDuration)
		
		fmt.Printf("   各数据源结果:\n")
		for _, source := range multiResult.Sources {
			status := map[bool]string{true: "✅", false: "❌"}[source.Success]
			fmt.Printf("     %s %s: 耗时%v\n", status, source.Source, source.Duration)
		}
	}

	// 3. 批量采集
	fmt.Println("\n📦 3. 批量采集")
	codes := []string{"BATCH-001", "BATCH-002", "BATCH-003"}
	batchResult, err := javScraperService.BatchScrapeMovies(codes)
	if err != nil {
		log.Printf("批量采集失败: %v", err)
	} else {
		fmt.Printf("   批量采集结果: 总数%d, 成功%d, 失败%d\n", 
			batchResult.TotalCodes, batchResult.SuccessCount, batchResult.FailureCount)
		fmt.Printf("   总耗时: %v\n", batchResult.TotalDuration)
		
		if len(batchResult.Errors) > 0 {
			fmt.Printf("   错误信息:\n")
			for _, errMsg := range batchResult.Errors {
				fmt.Printf("     - %s\n", errMsg)
			}
		}
	}

	// 4. 获取采集配置
	fmt.Println("\n⚙️ 4. 获取采集配置")
	config, err := javScraperService.GetScrapingConfig()
	if err != nil {
		log.Printf("获取采集配置失败: %v", err)
	} else {
		fmt.Printf("   采集配置:\n")
		fmt.Printf("     JavBus: %v, Javinizer: %v, JavSP: %v\n", 
			config.JavBusEnabled, config.JavinizerEnabled, config.JavSPEnabled)
		fmt.Printf("     超时时间: %v, 速率限制: %v, 最大重试: %d\n", 
			config.Timeout, config.RateLimit, config.MaxRetries)
	}

	// 5. 测试数据源
	fmt.Println("\n🔧 5. 测试数据源")
	sources := []string{"javbus", "javinizer", "javsp"}
	for _, source := range sources {
		testResult, err := javScraperService.TestScrapingSource(source)
		if err != nil {
			log.Printf("测试数据源%s失败: %v", source, err)
			continue
		}
		
		status := map[bool]string{true: "✅ 可用", false: "❌ 不可用"}[testResult.Available]
		fmt.Printf("   %s %s: 响应时间%v\n", status, source, testResult.ResponseTime)
		if testResult.Error != "" {
			fmt.Printf("     错误: %s\n", testResult.Error)
		}
	}
}

func demonstrateStatistics(javService service.JAVService) {
	fmt.Println("📊 统计功能演示")
	fmt.Println(strings.Repeat("-", 30))

	// 1. JAV统计信息
	fmt.Println("📈 1. JAV统计信息")
	javStats, err := javService.GetJAVStats()
	if err != nil {
		log.Printf("获取JAV统计失败: %v", err)
	} else {
		fmt.Printf("   影片统计:\n")
		fmt.Printf("     总影片数: %d\n", javStats.TotalMovies)
		fmt.Printf("     已完成: %d, 等待中: %d, 失败: %d\n", 
			javStats.CompletedMovies, javStats.PendingMovies, javStats.FailedMovies)
		
		if javStats.TotalMovies > 0 {
			completionRate := float64(javStats.CompletedMovies) / float64(javStats.TotalMovies) * 100
			fmt.Printf("     完成率: %.1f%%\n", completionRate)
		}
	}

	// 2. 采集统计信息
	fmt.Println("\n🕷️ 2. 采集统计信息")
	scrapingStats, err := javService.GetScrapingStats()
	if err != nil {
		log.Printf("获取采集统计失败: %v", err)
	} else {
		fmt.Printf("   采集统计:\n")
		fmt.Printf("     总采集数: %d\n", scrapingStats.TotalScrapes)
		fmt.Printf("     成功: %d, 失败: %d\n", 
			scrapingStats.SuccessfulScrapes, scrapingStats.FailedScrapes)
		fmt.Printf("     成功率: %.1f%%\n", scrapingStats.SuccessRate)
	}

	// 3. 可下载影片统计
	fmt.Println("\n💾 3. 可下载影片统计")
	downloadableMovies, total, err := javService.GetDownloadableMovies(0, 10)
	if err != nil {
		log.Printf("获取可下载影片失败: %v", err)
	} else {
		fmt.Printf("   可下载影片: %d部\n", total)
		for _, movie := range downloadableMovies {
			fmt.Printf("   - %s: %s (磁力链接: %d个)\n", 
				movie.Code, movie.Title, movie.MagnetCount)
		}
	}
}

// formatBytes 格式化字节大小
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}