package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"magnet-downloader/internal/api"
	"magnet-downloader/internal/api/middleware"
	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/database"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	fmt.Println("=== JAV API 演示程序 ===\n")

	// 启动API服务器
	go startAPIServer()

	// 等待服务器启动
	time.Sleep(2 * time.Second)

	// 演示API调用
	demonstrateJAVAPI()
}

func startAPIServer() {
	fmt.Println("🚀 启动API服务器...")

	// 初始化数据库
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建Repository和Services
	repo := repository.NewRepository(db)
	services, err := service.NewServices(repo, &config.Config{
		Database: config.DatabaseConfig{
			Type: "sqlite",
			DSN:  ":memory:",
		},
		JWT: config.JWTConfig{
			Secret:     "demo-secret-key",
			ExpireTime: 24 * time.Hour,
		},
		Server: config.ServerConfig{
			Port: 8080,
			Mode: "debug",
		},
	})
	if err != nil {
		log.Fatalf("服务初始化失败: %v", err)
	}

	// 初始化测试数据
	err = initTestData(repo)
	if err != nil {
		log.Fatalf("初始化测试数据失败: %v", err)
	}

	// 创建API路由
	router := api.NewRouter(services, &config.Config{
		JWT: config.JWTConfig{
			Secret:     "demo-secret-key",
			ExpireTime: 24 * time.Hour,
		},
		Server: config.ServerConfig{
			Port: 8080,
			Mode: "debug",
		},
	})

	// 启动服务器
	fmt.Println("✅ API服务器已启动: http://localhost:8080")
	log.Fatal(router.Run(":8080"))
}

func initDatabase() (*gorm.DB, error) {
	// 使用内存SQLite数据库进行演示
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	err = db.AutoMigrate(
		&model.JAVMovie{},
		&model.JAVActor{},
		&model.JAVMagnet{},
		&model.JAVGenre{},
		&model.DownloadTask{},
		&model.User{},
	)
	if err != nil {
		return nil, err
	}

	return db, nil
}

func initTestData(repo repository.Repository) error {
	// 创建测试用户
	user := &model.User{
		Username: "demo_user",
		Email:    "<EMAIL>",
		Role:     model.UserRoleUser,
	}
	err := repo.User().Create(user)
	if err != nil {
		return fmt.Errorf("创建用户失败: %w", err)
	}

	// 创建管理员用户
	admin := &model.User{
		Username: "admin",
		Email:    "<EMAIL>",
		Role:     model.UserRoleAdmin,
	}
	err = repo.User().Create(admin)
	if err != nil {
		return fmt.Errorf("创建管理员失败: %w", err)
	}

	// 创建测试影片
	releaseDate1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	releaseDate2 := time.Date(2024, 2, 20, 0, 0, 0, 0, time.UTC)

	movies := []*model.JAVMovie{
		{
			Code:           "DEMO-001",
			Title:          "演示影片1：青春校园",
			TitleEn:        "Demo Movie 1: School Days",
			Studio:         "演示工作室A",
			ReleaseDate:    &releaseDate1,
			Duration:       120,
			Rating:         8.5,
			Plot:           "一部关于青春校园生活的影片",
			PlotEn:         "A movie about school life",
			CoverURL:       "https://example.com/cover1.jpg",
			PosterURL:      "https://example.com/poster1.jpg",
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavBus,
		},
		{
			Code:           "DEMO-002",
			Title:          "演示影片2：都市恋情",
			TitleEn:        "Demo Movie 2: City Romance",
			Studio:         "演示工作室B",
			ReleaseDate:    &releaseDate2,
			Duration:       95,
			Rating:         7.8,
			Plot:           "一部关于都市恋情的影片",
			PlotEn:         "A movie about city romance",
			CoverURL:       "https://example.com/cover2.jpg",
			PosterURL:      "https://example.com/poster2.jpg",
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavinizer,
		},
	}

	for _, movie := range movies {
		err := repo.JAVMovie().Create(movie)
		if err != nil {
			return fmt.Errorf("创建影片失败: %w", err)
		}
	}

	// 创建测试演员
	debutDate := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	actors := []*model.JAVActor{
		{
			Name:      "演示演员A",
			NameEn:    "Demo Actor A",
			NameJp:    "デモ俳優A",
			Height:    165,
			Bust:      88,
			Waist:     58,
			Hip:       85,
			BloodType: "A",
			Hobby:     "读书、音乐",
			DebutDate: &debutDate,
		},
		{
			Name:      "演示演员B",
			NameEn:    "Demo Actor B",
			NameJp:    "デモ俳優B",
			Height:    160,
			Bust:      85,
			Waist:     55,
			Hip:       82,
			BloodType: "B",
			Hobby:     "旅行、摄影",
			DebutDate: &debutDate,
		},
	}

	for _, actor := range actors {
		err := repo.JAVActor().Create(actor)
		if err != nil {
			return fmt.Errorf("创建演员失败: %w", err)
		}
	}

	// 创建测试分类
	genres := []*model.JAVGenre{
		{
			Name:        "校园",
			NameEn:      "School",
			NameJp:      "学校",
			Description: "校园题材影片",
		},
		{
			Name:        "都市",
			NameEn:      "Urban",
			NameJp:      "都市",
			Description: "都市题材影片",
		},
	}

	for _, genre := range genres {
		err := repo.JAVGenre().Create(genre)
		if err != nil {
			return fmt.Errorf("创建分类失败: %w", err)
		}
	}

	// 创建测试磁力链接
	magnets := []*model.JAVMagnet{
		{
			MovieID:          movies[0].ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo001",
			FileName:         "DEMO-001.1080p.中文字幕.mp4",
			FileSize:         3221225472, // 3GB
			Quality:          model.JAVMagnetQuality1080p,
			HasSubtitle:      true,
			SubtitleLanguage: model.JAVMagnetSubtitleLanguageChinese,
			Source:           "javbus",
			Uploader:         "demo_uploader",
			Seeders:          50,
			Leechers:         10,
			Score:            95.0,
		},
		{
			MovieID:          movies[1].ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo002",
			FileName:         "DEMO-002.720p.mp4",
			FileSize:         1610612736, // 1.5GB
			Quality:          model.JAVMagnetQuality720p,
			HasSubtitle:      false,
			SubtitleLanguage: model.JAVMagnetSubtitleLanguageNone,
			Source:           "javinizer",
			Uploader:         "javinizer_bot",
			Seeders:          30,
			Leechers:         8,
			Score:            80.0,
		},
	}

	for _, magnet := range magnets {
		err := repo.JAVMagnet().Create(magnet)
		if err != nil {
			return fmt.Errorf("创建磁力链接失败: %w", err)
		}
	}

	return nil
}

func demonstrateJAVAPI() {
	fmt.Println("🌐 开始演示JAV API调用...\n")

	baseURL := "http://localhost:8080/api/v1/jav"

	// 演示公开接口
	demonstratePublicAPIs(baseURL)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示需要认证的接口
	demonstrateAuthenticatedAPIs(baseURL)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示管理员接口
	demonstrateAdminAPIs(baseURL)
}

func demonstratePublicAPIs(baseURL string) {
	fmt.Println("📖 演示公开API接口")
	fmt.Println(strings.Repeat("-", 30))

	// 1. 获取最新影片
	fmt.Println("1. 获取最新影片")
	response := makeRequest("GET", baseURL+"/movies/latest?limit=5", "", nil)
	printResponse("最新影片", response)

	// 2. 获取热门影片
	fmt.Println("\n2. 获取热门影片")
	response = makeRequest("GET", baseURL+"/movies/popular?limit=3", "", nil)
	printResponse("热门影片", response)

	// 3. 获取所有分类
	fmt.Println("\n3. 获取所有分类")
	response = makeRequest("GET", baseURL+"/genres", "", nil)
	printResponse("分类列表", response)

	// 4. 获取热门分类
	fmt.Println("\n4. 获取热门分类")
	response = makeRequest("GET", baseURL+"/genres/popular?limit=5", "", nil)
	printResponse("热门分类", response)

	// 5. 获取统计信息
	fmt.Println("\n5. 获取统计信息")
	response = makeRequest("GET", baseURL+"/stats", "", nil)
	printResponse("统计信息", response)
}

func demonstrateAuthenticatedAPIs(baseURL string) {
	fmt.Println("🔒 演示需要认证的API接口")
	fmt.Println(strings.Repeat("-", 30))

	// 模拟JWT Token（在实际应用中需要通过登录获取）
	token := "Bearer demo-jwt-token"

	// 1. 搜索影片
	fmt.Println("1. 搜索影片")
	response := makeRequest("GET", baseURL+"/movies/search?keyword=演示&page=1&page_size=10", token, nil)
	printResponse("搜索结果", response)

	// 2. 获取影片详情
	fmt.Println("\n2. 获取影片详情")
	response = makeRequest("GET", baseURL+"/movies/DEMO-001", token, nil)
	printResponse("影片详情", response)

	// 3. 获取影片磁力链接
	fmt.Println("\n3. 获取影片磁力链接")
	response = makeRequest("GET", baseURL+"/movies/DEMO-001/magnets", token, nil)
	printResponse("磁力链接", response)

	// 4. 获取最佳磁力链接
	fmt.Println("\n4. 获取最佳磁力链接")
	response = makeRequest("GET", baseURL+"/movies/DEMO-001/best-magnet", token, nil)
	printResponse("最佳磁力链接", response)

	// 5. 搜索演员
	fmt.Println("\n5. 搜索演员")
	response = makeRequest("GET", baseURL+"/actors/search?keyword=演示&page=1&page_size=5", token, nil)
	printResponse("演员搜索", response)

	// 6. 根据清晰度获取磁力链接
	fmt.Println("\n6. 根据清晰度获取磁力链接")
	response = makeRequest("GET", baseURL+"/magnets/quality/1080p?page=1&page_size=5", token, nil)
	printResponse("1080p磁力链接", response)
}

func demonstrateAdminAPIs(baseURL string) {
	fmt.Println("👑 演示管理员API接口")
	fmt.Println(strings.Repeat("-", 30))

	// 模拟管理员JWT Token
	adminToken := "Bearer admin-jwt-token"

	// 1. 触发数据采集
	fmt.Println("1. 触发数据采集")
	requestBody := map[string]string{
		"code": "NEW-001",
	}
	response := makeRequest("POST", baseURL+"/scraping/trigger", adminToken, requestBody)
	printResponse("采集结果", response)

	// 2. 批量数据采集
	fmt.Println("\n2. 批量数据采集")
	batchRequestBody := map[string][]string{
		"codes": {"BATCH-001", "BATCH-002", "BATCH-003"},
	}
	response = makeRequest("POST", baseURL+"/scraping/batch", adminToken, batchRequestBody)
	printResponse("批量采集结果", response)

	// 3. 获取采集统计
	fmt.Println("\n3. 获取采集统计")
	response = makeRequest("GET", baseURL+"/scraping/stats", adminToken, nil)
	printResponse("采集统计", response)
}

func makeRequest(method, url, token string, body interface{}) map[string]interface{} {
	var reqBody io.Reader
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(method, url, reqBody)
	if err != nil {
		return map[string]interface{}{
			"error": fmt.Sprintf("创建请求失败: %v", err),
		}
	}

	if token != "" {
		req.Header.Set("Authorization", token)
	}
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return map[string]interface{}{
			"error": fmt.Sprintf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()

	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return map[string]interface{}{
			"error": fmt.Sprintf("读取响应失败: %v", err),
		}
	}

	var result map[string]interface{}
	if err := json.Unmarshal(responseBody, &result); err != nil {
		return map[string]interface{}{
			"error":        fmt.Sprintf("解析响应失败: %v", err),
			"raw_response": string(responseBody),
			"status_code":  resp.StatusCode,
		}
	}

	result["status_code"] = resp.StatusCode
	return result
}

func printResponse(title string, response map[string]interface{}) {
	fmt.Printf("   📋 %s:\n", title)

	statusCode, ok := response["status_code"].(int)
	if ok {
		statusIcon := "✅"
		if statusCode >= 400 {
			statusIcon = "❌"
		}
		fmt.Printf("      %s 状态码: %d\n", statusIcon, statusCode)
	}

	if errorMsg, exists := response["error"]; exists {
		fmt.Printf("      ❌ 错误: %v\n", errorMsg)
		return
	}

	success, ok := response["success"].(bool)
	if ok {
		successIcon := "✅"
		if !success {
			successIcon = "❌"
		}
		fmt.Printf("      %s 成功: %v\n", successIcon, success)
	}

	if message, exists := response["message"]; exists {
		fmt.Printf("      💬 消息: %v\n", message)
	}

	if data, exists := response["data"]; exists {
		switch v := data.(type) {
		case []interface{}:
			fmt.Printf("      📊 数据: 数组，包含%d个项目\n", len(v))
			if len(v) > 0 {
				if item, ok := v[0].(map[string]interface{}); ok {
					if code, exists := item["code"]; exists {
						fmt.Printf("         首项: %v\n", code)
					} else if name, exists := item["name"]; exists {
						fmt.Printf("         首项: %v\n", name)
					}
				}
			}
		case map[string]interface{}:
			fmt.Printf("      📊 数据: 对象\n")
			if code, exists := v["code"]; exists {
				fmt.Printf("         番号: %v\n", code)
			}
			if title, exists := v["title"]; exists {
				fmt.Printf("         标题: %v\n", title)
			}
			if total, exists := v["total"]; exists {
				fmt.Printf("         总数: %v\n", total)
			}
			if totalMovies, exists := v["total_movies"]; exists {
				fmt.Printf("         总影片数: %v\n", totalMovies)
			}
		default:
			fmt.Printf("      📊 数据: %v\n", data)
		}
	}
}