package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"magnet-downloader/internal/model"
	"magnet-downloader/internal/repository"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	fmt.Println("=== JAV Repository 演示程序 ===\n")

	// 初始化数据库
	db, err := initDatabase()
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建Repository
	repo := repository.NewRepository(db)

	// 演示影片Repository
	demonstrateMovieRepository(repo)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示演员Repository
	demonstrateActorRepository(repo)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示磁力链接Repository
	demonstrateMagnetRepository(repo)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示分类Repository
	demonstrateGenreRepository(repo)

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示事务功能
	demonstrateTransaction(repo)
}

func initDatabase() (*gorm.DB, error) {
	// 使用内存SQLite数据库进行演示
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	err = db.AutoMigrate(
		&model.JAVMovie{},
		&model.JAVActor{},
		&model.JAVMagnet{},
		&model.JAVGenre{},
	)
	if err != nil {
		return nil, err
	}

	fmt.Println("✅ 数据库初始化完成")
	return db, nil
}

func demonstrateMovieRepository(repo repository.Repository) {
	fmt.Println("🎬 影片Repository演示")
	fmt.Println(strings.Repeat("-", 30))

	movieRepo := repo.JAVMovie()

	// 创建测试影片
	releaseDate := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	movies := []*model.JAVMovie{
		{
			Code:           "DEMO-001",
			Title:          "演示影片1",
			TitleEn:        "Demo Movie 1",
			Studio:         "演示工作室",
			ReleaseDate:    &releaseDate,
			Duration:       120,
			Rating:         8.5,
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavBus,
		},
		{
			Code:           "DEMO-002",
			Title:          "演示影片2",
			TitleEn:        "Demo Movie 2",
			Studio:         "另一个工作室",
			ReleaseDate:    &releaseDate,
			Duration:       90,
			Rating:         7.8,
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavinizer,
		},
	}

	// 创建影片
	for _, movie := range movies {
		err := movieRepo.Create(movie)
		if err != nil {
			log.Printf("创建影片失败: %v", err)
			continue
		}
		fmt.Printf("✅ 创建影片: %s - %s\n", movie.Code, movie.Title)
	}

	// 根据番号查询
	movie, err := movieRepo.GetByCode("DEMO-001")
	if err != nil {
		log.Printf("查询影片失败: %v", err)
	} else {
		fmt.Printf("📖 查询影片: %s - %s (评分: %.1f)\n", movie.Code, movie.Title, movie.Rating)
	}

	// 搜索影片
	searchResults, total, err := movieRepo.Search("演示", 0, 10)
	if err != nil {
		log.Printf("搜索影片失败: %v", err)
	} else {
		fmt.Printf("🔍 搜索结果: 找到%d部影片\n", total)
		for _, result := range searchResults {
			fmt.Printf("   - %s: %s\n", result.Code, result.Title)
		}
	}

	// 按工作室查询
	studioMovies, studioTotal, err := movieRepo.GetByStudio("演示工作室", 0, 10)
	if err != nil {
		log.Printf("按工作室查询失败: %v", err)
	} else {
		fmt.Printf("🏢 工作室影片: 找到%d部影片\n", studioTotal)
		for _, studioMovie := range studioMovies {
			fmt.Printf("   - %s: %s\n", studioMovie.Code, studioMovie.Title)
		}
	}

	// 统计信息
	completedCount, _ := movieRepo.CountByStatus(model.JAVScrapingStatusCompleted)
	javbusCount, _ := movieRepo.CountBySource(model.JAVScrapingSourceJavBus)
	fmt.Printf("📊 统计信息: 已完成%d部, JavBus来源%d部\n", completedCount, javbusCount)
}

func demonstrateActorRepository(repo repository.Repository) {
	fmt.Println("👤 演员Repository演示")
	fmt.Println(strings.Repeat("-", 30))

	actorRepo := repo.JAVActor()

	// 创建测试演员
	debutDate := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	actors := []*model.JAVActor{
		{
			Name:      "演示演员A",
			NameEn:    "Demo Actor A",
			NameJp:    "デモ俳優A",
			Height:    165,
			Bust:      88,
			Waist:     58,
			Hip:       85,
			BloodType: "A",
			Hobby:     "读书",
			DebutDate: &debutDate,
		},
		{
			Name:      "演示演员B",
			NameEn:    "Demo Actor B",
			NameJp:    "デモ俳優B",
			Height:    160,
			Bust:      85,
			Waist:     55,
			Hip:       82,
			BloodType: "B",
			Hobby:     "音乐",
			DebutDate: &debutDate,
		},
	}

	// 创建演员
	for _, actor := range actors {
		err := actorRepo.Create(actor)
		if err != nil {
			log.Printf("创建演员失败: %v", err)
			continue
		}
		fmt.Printf("✅ 创建演员: %s (%s)\n", actor.Name, actor.NameEn)
	}

	// 根据姓名查询
	actor, err := actorRepo.GetByName("演示演员A")
	if err != nil {
		log.Printf("查询演员失败: %v", err)
	} else {
		fmt.Printf("📖 查询演员: %s - 身高%dcm, 血型%s\n", actor.Name, actor.Height, actor.BloodType)
	}

	// 搜索演员
	searchResults, total, err := actorRepo.Search("演示", 0, 10)
	if err != nil {
		log.Printf("搜索演员失败: %v", err)
	} else {
		fmt.Printf("🔍 搜索结果: 找到%d位演员\n", total)
		for _, result := range searchResults {
			fmt.Printf("   - %s (%s)\n", result.Name, result.NameEn)
		}
	}

	// 列表查询
	allActors, allTotal, err := actorRepo.List(0, 10, nil)
	if err != nil {
		log.Printf("列表查询失败: %v", err)
	} else {
		fmt.Printf("📋 演员列表: 共%d位演员\n", allTotal)
		for _, listActor := range allActors {
			fmt.Printf("   - %s: 身高%dcm, 三围%d-%d-%d\n", 
				listActor.Name, listActor.Height, listActor.Bust, listActor.Waist, listActor.Hip)
		}
	}
}

func demonstrateMagnetRepository(repo repository.Repository) {
	fmt.Println("🧲 磁力链接Repository演示")
	fmt.Println(strings.Repeat("-", 30))

	movieRepo := repo.JAVMovie()
	magnetRepo := repo.JAVMagnet()

	// 获取第一部影片
	movie, err := movieRepo.GetByCode("DEMO-001")
	if err != nil {
		log.Printf("获取影片失败: %v", err)
		return
	}

	// 创建测试磁力链接
	magnets := []*model.JAVMagnet{
		{
			MovieID:          movie.ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo1",
			FileName:         "DEMO-001.720p.mp4",
			FileSize:         1610612736, // 1.5GB
			Quality:          "720p",
			HasSubtitle:      false,
			SubtitleLanguage: "none",
			Source:           "unknown",
			Uploader:         "demo_uploader",
			Seeders:          20,
			Leechers:         5,
			Score:            65.0,
		},
		{
			MovieID:          movie.ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo2",
			FileName:         "DEMO-001.1080p.中文字幕.mp4",
			FileSize:         3221225472, // 3GB
			Quality:          "1080p",
			HasSubtitle:      true,
			SubtitleLanguage: "chinese",
			Source:           "javbus",
			Uploader:         "javbus_uploader",
			Seeders:          50,
			Leechers:         10,
			Score:            95.0,
		},
		{
			MovieID:          movie.ID,
			MagnetURL:        "magnet:?xt=urn:btih:demo3",
			FileName:         "DEMO-001.4K.中文字幕[FHD].mp4",
			FileSize:         5368709120, // 5GB
			Quality:          "4K",
			HasSubtitle:      true,
			SubtitleLanguage: "chinese",
			Source:           "FHD",
			Uploader:         "fhd_uploader",
			Seeders:          100,
			Leechers:         20,
			Score:            100.0,
		},
	}

	// 创建磁力链接
	for _, magnet := range magnets {
		err := magnetRepo.Create(magnet)
		if err != nil {
			log.Printf("创建磁力链接失败: %v", err)
			continue
		}
		fmt.Printf("✅ 创建磁力链接: %s (评分: %.1f)\n", magnet.FileName, magnet.Score)
	}

	// 获取影片的所有磁力链接
	movieMagnets, err := magnetRepo.GetByMovieID(movie.ID)
	if err != nil {
		log.Printf("获取影片磁力链接失败: %v", err)
	} else {
		fmt.Printf("📖 影片磁力链接: 共%d个\n", len(movieMagnets))
		for _, movieMagnet := range movieMagnets {
			fmt.Printf("   - %s: %s, 评分%.1f\n", 
				movieMagnet.Quality, formatBytes(movieMagnet.FileSize), movieMagnet.Score)
		}
	}

	// 获取最佳磁力链接
	bestMagnet, err := magnetRepo.GetBestMagnetByMovie(movie.ID)
	if err != nil {
		log.Printf("获取最佳磁力链接失败: %v", err)
	} else {
		fmt.Printf("🏆 最佳磁力链接: %s (评分: %.1f)\n", bestMagnet.FileName, bestMagnet.Score)
	}

	// 按清晰度查询
	hdMagnets, hdTotal, err := magnetRepo.GetByQuality("1080p", 0, 10)
	if err != nil {
		log.Printf("按清晰度查询失败: %v", err)
	} else {
		fmt.Printf("📺 1080p磁力链接: 共%d个\n", hdTotal)
		for _, hdMagnet := range hdMagnets {
			fmt.Printf("   - %s: %s\n", hdMagnet.FileName, formatBytes(hdMagnet.FileSize))
		}
	}

	// 统计信息
	movieMagnetCount, _ := magnetRepo.CountByMovieID(movie.ID)
	avgScore, _ := magnetRepo.GetAverageScoreByMovie(movie.ID)
	fmt.Printf("📊 统计信息: 影片磁力链接%d个, 平均评分%.2f\n", movieMagnetCount, avgScore)
}

func demonstrateGenreRepository(repo repository.Repository) {
	fmt.Println("🏷️ 分类Repository演示")
	fmt.Println(strings.Repeat("-", 30))

	genreRepo := repo.JAVGenre()

	// 创建测试分类
	genres := []*model.JAVGenre{
		{
			Name:        "动作",
			NameEn:      "Action",
			NameJp:      "アクション",
			Description: "动作类影片",
		},
		{
			Name:        "剧情",
			NameEn:      "Drama",
			NameJp:      "ドラマ",
			Description: "剧情类影片",
		},
		{
			Name:        "喜剧",
			NameEn:      "Comedy",
			NameJp:      "コメディ",
			Description: "喜剧类影片",
		},
	}

	// 创建分类
	for _, genre := range genres {
		err := genreRepo.Create(genre)
		if err != nil {
			log.Printf("创建分类失败: %v", err)
			continue
		}
		fmt.Printf("✅ 创建分类: %s (%s)\n", genre.Name, genre.NameEn)
	}

	// 根据名称查询
	genre, err := genreRepo.GetByName("动作")
	if err != nil {
		log.Printf("查询分类失败: %v", err)
	} else {
		fmt.Printf("📖 查询分类: %s - %s\n", genre.Name, genre.Description)
	}

	// 搜索分类
	searchResults, total, err := genreRepo.Search("剧", 0, 10)
	if err != nil {
		log.Printf("搜索分类失败: %v", err)
	} else {
		fmt.Printf("🔍 搜索结果: 找到%d个分类\n", total)
		for _, result := range searchResults {
			fmt.Printf("   - %s (%s)\n", result.Name, result.NameEn)
		}
	}

	// 列表查询
	allGenres, allTotal, err := genreRepo.List(0, 10, nil)
	if err != nil {
		log.Printf("列表查询失败: %v", err)
	} else {
		fmt.Printf("📋 分类列表: 共%d个分类\n", allTotal)
		for _, listGenre := range allGenres {
			fmt.Printf("   - %s: %s\n", listGenre.Name, listGenre.Description)
		}
	}
}

func demonstrateTransaction(repo repository.Repository) {
	fmt.Println("💳 事务功能演示")
	fmt.Println(strings.Repeat("-", 30))

	// 演示事务回滚
	fmt.Println("🔄 演示事务回滚...")
	err := repo.Transaction(func(txRepo repository.Repository) error {
		// 在事务中创建影片
		movie := &model.JAVMovie{
			Code:           "TX-ROLLBACK",
			Title:          "事务回滚测试",
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavBus,
		}
		
		err := txRepo.JAVMovie().Create(movie)
		if err != nil {
			return err
		}
		
		fmt.Printf("   ✅ 事务中创建影片: %s\n", movie.Code)
		
		// 故意返回错误以触发回滚
		return fmt.Errorf("故意触发回滚")
	})
	
	if err != nil {
		fmt.Printf("   ❌ 事务回滚: %v\n", err)
	}
	
	// 验证回滚效果
	_, err = repo.JAVMovie().GetByCode("TX-ROLLBACK")
	if err != nil {
		fmt.Printf("   ✅ 回滚成功: 影片未被保存\n")
	} else {
		fmt.Printf("   ❌ 回滚失败: 影片仍然存在\n")
	}

	// 演示事务提交
	fmt.Println("💾 演示事务提交...")
	err = repo.Transaction(func(txRepo repository.Repository) error {
		// 在事务中创建影片和演员
		movie := &model.JAVMovie{
			Code:           "TX-COMMIT",
			Title:          "事务提交测试",
			ScrapingStatus: model.JAVScrapingStatusCompleted,
			ScrapingSource: model.JAVScrapingSourceJavBus,
		}
		
		err := txRepo.JAVMovie().Create(movie)
		if err != nil {
			return err
		}
		
		actor := &model.JAVActor{
			Name:   "事务演员",
			NameEn: "Transaction Actor",
		}
		
		err = txRepo.JAVActor().Create(actor)
		if err != nil {
			return err
		}
		
		fmt.Printf("   ✅ 事务中创建影片: %s\n", movie.Code)
		fmt.Printf("   ✅ 事务中创建演员: %s\n", actor.Name)
		
		// 正常返回，事务将被提交
		return nil
	})
	
	if err != nil {
		fmt.Printf("   ❌ 事务失败: %v\n", err)
	} else {
		fmt.Printf("   ✅ 事务提交成功\n")
	}
	
	// 验证提交效果
	movie, err := repo.JAVMovie().GetByCode("TX-COMMIT")
	if err == nil {
		fmt.Printf("   ✅ 提交成功: 影片已保存 - %s\n", movie.Title)
	} else {
		fmt.Printf("   ❌ 提交失败: 影片未找到\n")
	}
	
	actor, err := repo.JAVActor().GetByName("事务演员")
	if err == nil {
		fmt.Printf("   ✅ 提交成功: 演员已保存 - %s\n", actor.Name)
	} else {
		fmt.Printf("   ❌ 提交失败: 演员未找到\n")
	}
}

// formatBytes 格式化字节大小
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}