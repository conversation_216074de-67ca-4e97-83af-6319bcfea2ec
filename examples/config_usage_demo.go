package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"magnet-downloader/internal/config"

	"github.com/spf13/viper"
)

func main() {
	fmt.Println("=== JAV配置系统演示程序 ===\n")

	// 演示1: 加载默认配置
	demonstrateDefaultConfig()

	fmt.Println("\n" + "="*50 + "\n")

	// 演示2: 加载自定义配置
	demonstrateCustomConfig()

	fmt.Println("\n" + "="*50 + "\n")

	// 演示3: 环境变量覆盖
	demonstrateEnvOverride()

	fmt.Println("\n" + "="*50 + "\n")

	// 演示4: 配置验证
	demonstrateConfigValidation()

	fmt.Println("\n" + "="*50 + "\n")

	// 演示5: 配置热重载
	demonstrateConfigReload()
}

func demonstrateDefaultConfig() {
	fmt.Println("📋 演示1: 加载默认配置")
	fmt.Println("-" * 30)

	// 重置viper并设置默认值
	viper.Reset()
	// 手动设置一些JAV默认值进行演示
	viper.SetDefault("jav.enabled", false)
	viper.SetDefault("jav.scraping.timeout", 30)
	viper.SetDefault("jav.download.prefer_quality", "1080p")
	viper.SetDefault("jav.api.max_search_results", 100)
	viper.SetDefault("jav.scraping.sources.javbus.enabled", true)
	viper.SetDefault("jav.scraping.sources.javbus.priority", 9)
	viper.SetDefault("jav.scraping.sources.javbus.weight", 0.4)

	// 显示一些默认配置值
	fmt.Printf("JAV功能默认状态: %v\n", viper.GetBool("jav.enabled"))
	fmt.Printf("数据采集超时时间: %d秒\n", viper.GetInt("jav.scraping.timeout"))
	fmt.Printf("首选视频质量: %s\n", viper.GetString("jav.download.prefer_quality"))
	fmt.Printf("最大搜索结果数: %d\n", viper.GetInt("jav.api.max_search_results"))
	fmt.Printf("JavBus数据源状态: %v\n", viper.GetBool("jav.scraping.sources.javbus.enabled"))
	fmt.Printf("JavBus优先级: %d\n", viper.GetInt("jav.scraping.sources.javbus.priority"))
	fmt.Printf("JavBus权重: %.1f\n", viper.GetFloat64("jav.scraping.sources.javbus.weight"))
}

func demonstrateCustomConfig() {
	fmt.Println("⚙️ 演示2: 加载自定义配置")
	fmt.Println("-" * 30)

	// 创建临时配置文件
	configContent := `
jav:
  enabled: true
  scraping:
    enabled: true
    timeout: 45
    rate_limit: 1
    min_confidence: 0.8
    sources:
      javbus:
        enabled: true
        priority: 10
        weight: 0.5
      javinizer:
        enabled: false
  download:
    enabled: true
    prefer_quality: "4K"
    max_concurrent: 5
  api:
    max_search_results: 200
    cache_enabled: false
`

	tmpFile, err := os.CreateTemp("", "jav_config_demo_*.yaml")
	if err != nil {
		log.Printf("创建临时文件失败: %v", err)
		return
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(configContent); err != nil {
		log.Printf("写入配置失败: %v", err)
		return
	}
	tmpFile.Close()

	// 加载自定义配置
	viper.Reset()
	viper.SetConfigFile(tmpFile.Name())
	// 设置默认值
	viper.SetDefault("jav.enabled", false)
	viper.SetDefault("jav.scraping.timeout", 30)
	viper.SetDefault("jav.download.prefer_quality", "1080p")
	viper.SetDefault("jav.api.max_search_results", 100)

	if err := viper.ReadInConfig(); err != nil {
		log.Printf("读取配置失败: %v", err)
		return
	}

	// 显示自定义配置值
	fmt.Printf("JAV功能状态: %v\n", viper.GetBool("jav.enabled"))
	fmt.Printf("数据采集超时时间: %d秒\n", viper.GetInt("jav.scraping.timeout"))
	fmt.Printf("最小可信度: %.1f\n", viper.GetFloat64("jav.scraping.min_confidence"))
	fmt.Printf("首选视频质量: %s\n", viper.GetString("jav.download.prefer_quality"))
	fmt.Printf("最大并发下载: %d\n", viper.GetInt("jav.download.max_concurrent"))
	fmt.Printf("最大搜索结果数: %d\n", viper.GetInt("jav.api.max_search_results"))
	fmt.Printf("缓存启用状态: %v\n", viper.GetBool("jav.api.cache_enabled"))
	fmt.Printf("JavBus权重: %.1f\n", viper.GetFloat64("jav.scraping.sources.javbus.weight"))
	fmt.Printf("Javinizer启用状态: %v\n", viper.GetBool("jav.scraping.sources.javinizer.enabled"))
}

func demonstrateEnvOverride() {
	fmt.Println("🌍 演示3: 环境变量覆盖")
	fmt.Println("-" * 30)

	// 设置环境变量
	os.Setenv("MD_JAV_ENABLED", "true")
	os.Setenv("MD_JAV_SCRAPING_TIMEOUT", "60")
	os.Setenv("MD_JAV_DOWNLOAD_PREFER_QUALITY", "720p")
	os.Setenv("MD_JAV_API_MAX_SEARCH_RESULTS", "50")

	// 重置viper并启用环境变量
	viper.Reset()
	viper.SetEnvPrefix("MD")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()
	// 设置默认值
	viper.SetDefault("jav.enabled", false)
	viper.SetDefault("jav.scraping.timeout", 30)
	viper.SetDefault("jav.download.prefer_quality", "1080p")
	viper.SetDefault("jav.api.max_search_results", 100)

	// 显示被环境变量覆盖的配置值
	fmt.Printf("JAV功能状态 (环境变量): %v\n", viper.GetBool("jav.enabled"))
	fmt.Printf("数据采集超时时间 (环境变量): %d秒\n", viper.GetInt("jav.scraping.timeout"))
	fmt.Printf("首选视频质量 (环境变量): %s\n", viper.GetString("jav.download.prefer_quality"))
	fmt.Printf("最大搜索结果数 (环境变量): %d\n", viper.GetInt("jav.api.max_search_results"))

	// 清理环境变量
	os.Unsetenv("MD_JAV_ENABLED")
	os.Unsetenv("MD_JAV_SCRAPING_TIMEOUT")
	os.Unsetenv("MD_JAV_DOWNLOAD_PREFER_QUALITY")
	os.Unsetenv("MD_JAV_API_MAX_SEARCH_RESULTS")
}

func demonstrateConfigValidation() {
	fmt.Println("✅ 演示4: 配置验证")
	fmt.Println("-" * 30)

	// 测试有效配置
	validConfig := &config.JAVConfig{
		Enabled: true,
		Scraping: config.JAVScrapingConfig{
			Enabled:         true,
			Timeout:         30,
			RateLimit:       2,
			MaxRetries:      3,
			MinConfidence:   0.7,
			BatchSize:       10,
			ConcurrentLimit: 5,
			Sources: config.JAVScrapingSourceConfig{
				JavBus: config.JAVSourceConfig{
					Enabled:    true,
					BaseURL:    "https://www.javbus.com",
					Timeout:    30,
					MaxRetries: 3,
					Priority:   9,
					Weight:     0.4,
					RateLimit:  2,
				},
			},
		},
		Download: config.JAVDownloadConfig{
			Enabled:       true,
			DefaultPath:   "/downloads/jav",
			PreferQuality: "1080p",
			MaxConcurrent: 3,
		},
		Storage: config.JAVStorageConfig{
			ImageQuality:  85,
			ThumbnailSize: 300,
		},
		Processing: config.JAVProcessConfig{
			ThumbnailCount:  10,
			ThumbnailWidth:  320,
			ThumbnailHeight: 180,
			PreviewDuration: 30,
		},
		API: config.JAVAPIConfig{
			MaxSearchResults: 100,
			MaxPageSize:      50,
			CacheTTL:         3600,
			RateLimitRPM:     1000,
		},
	}

	if err := validConfig.Validate(); err != nil {
		fmt.Printf("❌ 有效配置验证失败: %v\n", err)
	} else {
		fmt.Printf("✅ 有效配置验证通过\n")
	}

	// 测试无效配置
	invalidConfig := &config.JAVConfig{
		Enabled: true,
		Scraping: config.JAVScrapingConfig{
			Enabled: true,
			Timeout: 400, // 超出范围
		},
	}

	if err := invalidConfig.Validate(); err != nil {
		fmt.Printf("✅ 无效配置验证正确失败: %v\n", err)
	} else {
		fmt.Printf("❌ 无效配置验证意外通过\n")
	}

	// 测试禁用配置
	disabledConfig := &config.JAVConfig{
		Enabled: false,
	}

	if err := disabledConfig.Validate(); err != nil {
		fmt.Printf("❌ 禁用配置验证失败: %v\n", err)
	} else {
		fmt.Printf("✅ 禁用配置验证通过（跳过验证）\n")
	}
}

func demonstrateConfigReload() {
	fmt.Println("🔄 演示5: 配置热重载")
	fmt.Println("-" * 30)

	// 创建初始配置文件
	initialConfig := `
jav:
  enabled: false
  scraping:
    timeout: 30
`

	tmpFile, err := os.CreateTemp("", "jav_reload_demo_*.yaml")
	if err != nil {
		log.Printf("创建临时文件失败: %v", err)
		return
	}
	defer os.Remove(tmpFile.Name())

	if _, err := tmpFile.WriteString(initialConfig); err != nil {
		log.Printf("写入初始配置失败: %v", err)
		return
	}
	tmpFile.Close()

	// 加载初始配置
	viper.Reset()
	viper.SetConfigFile(tmpFile.Name())
	// 设置默认值
	viper.SetDefault("jav.enabled", false)
	viper.SetDefault("jav.scraping.timeout", 30)

	if err := viper.ReadInConfig(); err != nil {
		log.Printf("读取初始配置失败: %v", err)
		return
	}

	fmt.Printf("初始配置 - JAV启用状态: %v\n", viper.GetBool("jav.enabled"))
	fmt.Printf("初始配置 - 采集超时: %d秒\n", viper.GetInt("jav.scraping.timeout"))

	// 修改配置文件
	updatedConfig := `
jav:
  enabled: true
  scraping:
    timeout: 45
`

	if err := os.WriteFile(tmpFile.Name(), []byte(updatedConfig), 0644); err != nil {
		log.Printf("更新配置文件失败: %v", err)
		return
	}

	// 等待文件写入完成
	time.Sleep(100 * time.Millisecond)

	// 重新加载配置
	if err := viper.ReadInConfig(); err != nil {
		log.Printf("重新加载配置失败: %v", err)
		return
	}

	fmt.Printf("更新配置 - JAV启用状态: %v\n", viper.GetBool("jav.enabled"))
	fmt.Printf("更新配置 - 采集超时: %d秒\n", viper.GetInt("jav.scraping.timeout"))

	fmt.Println("✅ 配置热重载演示完成")
}