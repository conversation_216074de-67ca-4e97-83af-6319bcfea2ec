package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"magnet-downloader/pkg/javscraper/merger"
	"magnet-downloader/pkg/javscraper/selector"
)

func main() {
	fmt.Println("=== 数据融合与磁力筛选算法演示 ===\n")

	// 演示数据融合算法
	demonstrateDataMerging()

	fmt.Println("\n" + strings.Repeat("=", 60) + "\n")

	// 演示磁力筛选算法
	demonstrateMagnetSelection()
}

func demonstrateDataMerging() {
	fmt.Println("📊 数据融合算法演示")
	fmt.Println(strings.Repeat("-", 30))

	// 创建数据融合器
	dataMerger := merger.NewDataMerger(nil)

	// 模拟来自不同数据源的影片信息
	movie1 := &merger.MovieInfo{
		Code:       "DEMO-001",
		Title:      "演示影片",
		Studio:     "演示工作室",
		Source:     "javbus",
		Confidence: 0.9,
		ScrapedAt:  time.Now(),
		Actors: []merger.ActorInfo{
			{Name: "演员A", NameEn: "Actor A"},
			{Name: "演员B"},
		},
		Genres: []merger.GenreInfo{
			{Name: "分类1", NameEn: "Genre 1"},
		},
	}

	movie2 := &merger.MovieInfo{
		Code:       "DEMO-001",
		TitleEn:    "Demo Movie",
		PlotEn:     "English plot description",
		Duration:   120,
		Source:     "javinizer",
		Confidence: 0.8,
		ScrapedAt:  time.Now(),
		Actors: []merger.ActorInfo{
			{Name: "演员A", Height: 165},
			{Name: "演员C", NameEn: "Actor C"},
		},
		Genres: []merger.GenreInfo{
			{Name: "分类1", Description: "分类描述"},
			{Name: "分类2", NameEn: "Genre 2"},
		},
	}

	movie3 := &merger.MovieInfo{
		Code:       "DEMO-001",
		Rating:     8.5,
		ReleaseDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC),
		Source:     "javsp",
		Confidence: 0.7,
		ScrapedAt:  time.Now(),
		Actors: []merger.ActorInfo{
			{Name: "演员D", NameEn: "Actor D"},
		},
	}

	fmt.Printf("输入数据源:\n")
	fmt.Printf("  - javbus: 标题='%s', 演员=%d个, 可信度=%.1f\n", movie1.Title, len(movie1.Actors), movie1.Confidence)
	fmt.Printf("  - javinizer: 英文标题='%s', 时长=%d分钟, 可信度=%.1f\n", movie2.TitleEn, movie2.Duration, movie2.Confidence)
	fmt.Printf("  - javsp: 评分=%.1f, 发布日期=%s, 可信度=%.1f\n", movie3.Rating, movie3.ReleaseDate.Format("2006-01-02"), movie3.Confidence)

	// 执行数据融合
	startTime := time.Now()
	result, err := dataMerger.MergeMovieInfos([]*merger.MovieInfo{movie1, movie2, movie3})
	mergeTime := time.Since(startTime)

	if err != nil {
		log.Fatalf("数据融合失败: %v", err)
	}

	fmt.Printf("\n融合结果:\n")
	fmt.Printf("  - 番号: %s\n", result.Code)
	fmt.Printf("  - 中文标题: %s\n", result.Title)
	fmt.Printf("  - 英文标题: %s\n", result.TitleEn)
	fmt.Printf("  - 时长: %d分钟\n", result.Duration)
	fmt.Printf("  - 评分: %.1f\n", result.Rating)
	fmt.Printf("  - 发布日期: %s\n", result.ReleaseDate.Format("2006-01-02"))
	fmt.Printf("  - 演员数量: %d个\n", len(result.Actors))
	fmt.Printf("  - 分类数量: %d个\n", len(result.Genres))
	fmt.Printf("  - 数据源: %v\n", result.Sources)
	fmt.Printf("  - 融合后可信度: %.2f\n", result.Confidence)
	fmt.Printf("  - 融合耗时: %v\n", mergeTime)

	// 显示演员详细信息
	fmt.Printf("\n演员详细信息:\n")
	for i, actor := range result.Actors {
		fmt.Printf("  %d. %s", i+1, actor.Name)
		if actor.NameEn != "" {
			fmt.Printf(" (%s)", actor.NameEn)
		}
		if actor.Height > 0 {
			fmt.Printf(", 身高: %dcm", actor.Height)
		}
		fmt.Println()
	}
}

func demonstrateMagnetSelection() {
	fmt.Println("🧲 磁力筛选算法演示")
	fmt.Println(strings.Repeat("-", 30))

	// 创建磁力筛选器
	magnetSelector := selector.NewMagnetSelector(nil)

	// 模拟磁力链接数据
	magnets := []*selector.MagnetInfo{
		{
			MagnetURL:        "magnet:?xt=urn:btih:demo1",
			FileName:         "DEMO-001.720p.mp4",
			FileSize:         1536 * 1024 * 1024, // 1.5GB
			Quality:          "720p",
			HasSubtitle:      false,
			SubtitleLanguage: "none",
			Source:           "unknown",
			Seeders:          20,
			Leechers:         5,
		},
		{
			MagnetURL:        "magnet:?xt=urn:btih:demo2",
			FileName:         "DEMO-001.1080p.中文字幕.mp4",
			FileSize:         3 * 1024 * 1024 * 1024, // 3GB
			Quality:          "1080p",
			HasSubtitle:      true,
			SubtitleLanguage: "chinese",
			Source:           "javbus",
			Seeders:          50,
			Leechers:         10,
		},
		{
			MagnetURL:        "magnet:?xt=urn:btih:demo3",
			FileName:         "DEMO-001.4K.中文字幕[FHD].mp4",
			FileSize:         5 * 1024 * 1024 * 1024, // 5GB
			Quality:          "4K",
			HasSubtitle:      true,
			SubtitleLanguage: "chinese",
			Source:           "FHD",
			Seeders:          100,
			Leechers:         20,
		},
		{
			MagnetURL:        "magnet:?xt=urn:btih:demo4",
			FileName:         "DEMO-001.1080p.English.Sub.mp4",
			FileSize:         2867 * 1024 * 1024, // 2.8GB
			Quality:          "1080p",
			HasSubtitle:      true,
			SubtitleLanguage: "english",
			Source:           "unknown",
			Seeders:          30,
			Leechers:         8,
		},
	}

	fmt.Printf("输入磁力链接:\n")
	for i, magnet := range magnets {
		fmt.Printf("  %d. %s\n", i+1, magnet.FileName)
		fmt.Printf("     大小: %s, 清晰度: %s, 字幕: %v (%s)\n", 
			formatBytes(magnet.FileSize), magnet.Quality, magnet.HasSubtitle, magnet.SubtitleLanguage)
		fmt.Printf("     来源: %s, 做种/下载: %d/%d\n", magnet.Source, magnet.Seeders, magnet.Leechers)
	}

	// 执行磁力筛选
	startTime := time.Now()
	result, err := magnetSelector.SelectBestMagnet(magnets)
	selectionTime := time.Since(startTime)

	if err != nil {
		log.Fatalf("磁力筛选失败: %v", err)
	}

	fmt.Printf("\n筛选结果:\n")
	fmt.Printf("  - 总数量: %d个\n", result.TotalCount)
	fmt.Printf("  - 有效数量: %d个\n", result.ValidCount)
	fmt.Printf("  - 筛选耗时: %v\n", selectionTime)

	if result.BestMagnet != nil {
		best := result.BestMagnet
		fmt.Printf("\n🏆 最佳磁力链接:\n")
		fmt.Printf("  - 文件名: %s\n", best.FileName)
		fmt.Printf("  - 文件大小: %s\n", formatBytes(best.FileSize))
		fmt.Printf("  - 清晰度: %s\n", best.Quality)
		fmt.Printf("  - 字幕: %v (%s)\n", best.HasSubtitle, best.SubtitleLanguage)
		fmt.Printf("  - 来源: %s\n", best.Source)
		fmt.Printf("  - 健康度: %d/%d (做种/下载)\n", best.Seeders, best.Leechers)
		fmt.Printf("  - 综合评分: %.2f分\n", best.FinalScore)
		fmt.Printf("  - 排名: 第%d名\n", best.Rank)

		fmt.Printf("\n评分详情:\n")
		fmt.Printf("  - 文件大小评分: %.1f分 (权重40%%)\n", best.SizeScore)
		fmt.Printf("  - 字幕评分: %.1f分 (权重35%%)\n", best.SubtitleScore)
		fmt.Printf("  - 清晰度评分: %.1f分 (权重15%%)\n", best.QualityScore)
		fmt.Printf("  - 来源评分: %.1f分 (权重10%%)\n", best.SourceScore)
		fmt.Printf("  - 健康度评分: %.1f分 (额外加分)\n", best.HealthScore)
	}

	// 显示所有磁力链接的排名
	fmt.Printf("\n完整排名:\n")
	for i, magnet := range result.AllMagnets {
		fmt.Printf("  %d. %s (%.2f分)\n", i+1, magnet.FileName, magnet.FinalScore)
	}

	// 显示筛选规则
	fmt.Printf("\n筛选规则:\n")
	for _, rule := range result.SelectionRules {
		fmt.Printf("  - %s\n", rule)
	}

	// 获取统计信息
	stats := magnetSelector.GetStatistics(result)
	fmt.Printf("\n统计信息:\n")
	fmt.Printf("  - 平均评分: %.2f分\n", stats["average_score"])
	fmt.Printf("  - 平均文件大小: %s\n", stats["average_size"])
	
	if qualityDist, ok := stats["quality_distribution"].(map[string]int); ok {
		fmt.Printf("  - 清晰度分布: ")
		for quality, count := range qualityDist {
			fmt.Printf("%s(%d个) ", quality, count)
		}
		fmt.Println()
	}
	
	if subtitleDist, ok := stats["subtitle_distribution"].(map[string]int); ok {
		fmt.Printf("  - 字幕分布: ")
		for subtitle, count := range subtitleDist {
			fmt.Printf("%s(%d个) ", subtitle, count)
		}
		fmt.Println()
	}
}

// formatBytes 格式化字节大小
func formatBytes(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}