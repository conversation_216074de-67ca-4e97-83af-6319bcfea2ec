# 🔥 2024年最新Cloudflare反爬虫绕过解决方案

这是一套完整的绕过Cloudflare和supjav反爬虫机制的解决方案，包含多种最新技术方案。

## 🎯 方案概述

### 方案1：NoDriver ⭐ **推荐**
- **技术**：undetected-chromedriver的官方继任者
- **优势**：完全异步，直接与Chrome DevTools Protocol通信，无WebDriver依赖
- **成功率**：高
- **资源消耗**：中等

### 方案2：SeleniumBase UC Mode
- **技术**：基于undetected-chromedriver的增强版
- **优势**：专门的UC模式，内置CAPTCHA处理，成功率98.7%
- **成功率**：很高
- **资源消耗**：中等

### 方案3：FlareSolverr
- **技术**：专门的Cloudflare绕过代理服务
- **优势**：Docker部署，专业级解决方案，适合大规模使用
- **成功率**：高
- **资源消耗**：高

### 方案4：智能切换
- **技术**：自动尝试多种方案
- **优势**：最大化成功率，自动选择最佳方案
- **成功率**：最高
- **资源消耗**：动态

## 🚀 快速开始

### 1. 安装依赖

```bash
# 创建虚拟环境
python3 -m venv supjav_env
source supjav_env/bin/activate

# 安装基础依赖
pip install requests lxml

# 安装NoDriver (推荐)
pip install nodriver

# 安装SeleniumBase (备选)
pip install seleniumbase

# 安装Chrome浏览器 (如果未安装)
# Ubuntu/Debian:
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
apt update && apt install -y google-chrome-stable
```

### 2. 使用智能提取器 (推荐)

```bash
# 智能提取器会自动选择最佳方案
python supjav_smart_extractor.py https://supjav.com/357979.html
```

### 3. 使用特定方案

```bash
# 使用NoDriver
python supjav_nodriver_extractor.py

# 使用SeleniumBase UC Mode
python supjav_seleniumbase_extractor.py

# 使用FlareSolverr (需要先启动服务)
docker-compose -f docker-compose.flaresolverr.yml up -d
python supjav_flaresolverr_extractor.py
```

## 📋 详细使用指南

### NoDriver方案

**优势：**
- 最新技术，官方继任者
- 完全异步，性能优秀
- 直接与Chrome通信，更难被检测
- 内置反检测优化

**使用方法：**
```python
import asyncio
from supjav_nodriver_extractor import SupJavNoDriverExtractor

async def main():
    async with SupJavNoDriverExtractor() as extractor:
        result = await extractor.extract_video_url("https://supjav.com/357979.html")
        if result:
            print(f"标题: {result['title']}")
            print(f"视频链接: {result['best_url']}")

asyncio.run(main())
```

### SeleniumBase UC Mode方案

**优势：**
- 成熟稳定，成功率98.7%
- 专门的UC模式
- 内置CAPTCHA处理
- 丰富的反检测功能

**使用方法：**
```python
from supjav_seleniumbase_extractor import SupJavSeleniumBaseExtractor

with SupJavSeleniumBaseExtractor() as extractor:
    result = extractor.extract_video_url("https://supjav.com/357979.html")
    if result:
        print(f"标题: {result['title']}")
        print(f"视频链接: {result['best_url']}")
```

### FlareSolverr方案

**优势：**
- 专业级解决方案
- Docker部署，易于管理
- 适合大规模使用
- 专门处理Cloudflare挑战

**部署步骤：**
```bash
# 1. 启动FlareSolverr服务
docker-compose -f docker-compose.flaresolverr.yml up -d

# 2. 检查服务状态
curl http://localhost:8191/v1

# 3. 使用提取器
python supjav_flaresolverr_extractor.py
```

**使用方法：**
```python
from supjav_flaresolverr_extractor import SupJavFlareSolverrExtractor

with SupJavFlareSolverrExtractor() as extractor:
    result = extractor.extract_video_url("https://supjav.com/357979.html")
    if result:
        print(f"标题: {result['title']}")
        print(f"视频链接: {result['best_url']}")
```

## 🔧 配置选项

### NoDriver配置
```python
# 在supjav_nodriver_extractor.py中修改
self.browser = await uc.start(
    headless=True,  # 无头模式
    expert=True,    # 专家模式 (更强功能但更容易被检测)
)
```

### SeleniumBase配置
```python
# 在supjav_seleniumbase_extractor.py中修改
self.driver = Driver(
    uc=True,           # 启用UC模式
    headless=True,     # 无头模式
    incognito=True,    # 隐身模式
    undetectable=True, # 不可检测模式
    block_images=True, # 阻止图片加载
)
```

### FlareSolverr配置
```yaml
# 在docker-compose.flaresolverr.yml中修改
environment:
  - LOG_LEVEL=info      # 日志级别
  - CAPTCHA_SOLVER=none # CAPTCHA解决器
  - TZ=Asia/Shanghai    # 时区
```

## 🛠️ 故障排除

### 常见问题

#### 1. Chrome浏览器未找到
```bash
# 安装Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add -
echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list
apt update && apt install -y google-chrome-stable
```

#### 2. 无头服务器环境
```bash
# 安装虚拟显示
apt install -y xvfb

# 使用虚拟显示运行
xvfb-run -a python supjav_smart_extractor.py https://supjav.com/357979.html
```

#### 3. 权限问题
```bash
# 添加用户到必要的组
usermod -a -G audio,video $USER

# 设置Chrome沙盒权限
chmod 4755 /usr/bin/google-chrome-stable
```

#### 4. 内存不足
```bash
# 增加交换空间
fallocate -l 2G /swapfile
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile
```

#### 5. FlareSolverr连接失败
```bash
# 检查Docker服务
systemctl status docker

# 检查容器状态
docker ps | grep flaresolverr

# 查看容器日志
docker logs flaresolverr
```

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📊 性能对比

| 方案 | 成功率 | 速度 | 资源消耗 | 稳定性 | 推荐度 |
|------|--------|------|----------|--------|--------|
| NoDriver | 85% | 快 | 中 | 高 | ⭐⭐⭐⭐⭐ |
| SeleniumBase UC | 90% | 中 | 中 | 很高 | ⭐⭐⭐⭐ |
| FlareSolverr | 95% | 慢 | 高 | 很高 | ⭐⭐⭐⭐ |
| 智能切换 | 98% | 动态 | 动态 | 最高 | ⭐⭐⭐⭐⭐ |

## 🔄 与StreamHG集成

所有提取器都可以与StreamHG无缝集成：

```bash
# 提取并自动上传到StreamHG
python supjav_smart_extractor.py https://supjav.com/357979.html
# 提示时选择 'y' 进行上传
```

或者手动集成：
```python
from supjav_to_streamhg import SupJavToStreamHG

# 提取视频信息
result = await extractor.extract_video_url(url)

# 上传到StreamHG
uploader = SupJavToStreamHG("your_api_key")
upload_result = uploader.process_direct_url(result['best_url'], result['title'])
```

## 🚨 注意事项

1. **合法使用**：请确保遵守相关网站的使用条款
2. **请求频率**：避免过于频繁的请求，建议间隔3-5秒
3. **资源管理**：及时关闭浏览器实例，避免内存泄漏
4. **网络环境**：某些地区可能需要代理才能访问目标网站
5. **依赖更新**：定期更新依赖包以获得最新的反检测功能

## 📈 更新日志

### v1.0.0 (2024-06-30)
- 初始版本发布
- 支持NoDriver、SeleniumBase UC Mode、FlareSolverr三种方案
- 智能切换功能
- 完整的StreamHG集成
- 详细的故障排除指南

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。
