# ===========================================
# JAV Magnet Downloader Project .gitignore
# ===========================================

# ============ Go 相关 ============
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
magnet-downloader
main
complete_javbus_scraper

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.out
coverage.html

# Go workspace file
go.work
go.work.sum

# Build directory
build/
dist/
bin/

# ============ IDE 和编辑器 ============
.vscode/
.idea/
.cursor/
*.swp
*.swo
*~
*.tmp

# ============ 操作系统文件 ============
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
.user.ini

# ============ 日志文件 ============
*.log
logs/
debug.log
server.log
javbus_scraping.log
nohup.out
fixed.log

# ============ 配置文件（敏感信息）============
config.yaml
config.yml
config.json
.env
.env.local
.env.production
.env.development
config.yaml.bak
config_optimized.yaml
streamhg_config.json

# ============ 数据库文件 ============
*.db
*.sqlite
*.sqlite3
jav_demo.db
real_jav.db
real_jav_v2.db

# ============ 下载和存储目录 ============
downloads/
downloaded_files/
storage/
temp/
tmp/
data/

# ============ 媒体文件 ============
# 视频文件
*.mp4
*.avi
*.mkv
*.mov
*.wmv
*.flv
*.webm
*.m4v
*.3gp
*.ts
*.m2ts

# 图片文件
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.webp
*.svg
*.ico

# 音频文件
*.mp3
*.wav
*.flac
*.aac
*.ogg
*.wma

# ============ 压缩文件 ============
*.zip
*.rar
*.7z
*.tar
*.tar.gz
*.tar.bz2
*.gz
*.bz2

# ============ Python 相关 ============
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
supjav_env/
.pytest_cache/
*.egg-info/

# ============ Node.js 相关 ============
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# ============ Docker 相关 ============
docker-compose.override.yml
.dockerignore

# ============ 证书和密钥 ============
*.pem
*.key
*.crt
*.p12
*.pfx

# ============ 备份文件 ============
*.bak
*.backup
*.old
*.orig

# ============ 临时文件 ============
*.tmp
*.temp
*.cache
.cache/

# ============ 测试文件 ============
test_*.go
*_test.go
test_*.py
verify_*.go
debug_*.go
simple_*.go
demo_*.go

# ============ 脚本和工具文件 ============
*.sh
*.bat
*.cmd
*.ps1

# ============ 特定项目文件 ============
# 外部scraper子模块（包含独立的git仓库）
external_scrapers/

# Aria2 配置
aria2/
aria2-*.conf
aria2_*.conf
qbittorrent-*.conf

# 115网盘相关
115_*.py
add_115_*.py
verify_115_*.py

# SupJAV相关
supjav_*.py
test_supjav_*.py

# 磁力链接相关
magnet_*.py
*magnet*.py

# 浏览器相关
test_browser.py
test_cloudflare_*.py

# VNC相关
vnc_*.py
setup_vnc.sh

# 部署脚本
deploy_*.sh
setup_*.sh

# 文档和说明（保留重要的）
# README.md - 保留
# LICENSE - 保留
# 其他临时文档
*_ANALYSIS*.md
*_GUIDE*.md
*_REPORT*.md
CLOUDFLARE_*.md
JAVINIZER_*.md
OPTIMIZATION_*.md
SYSTEM_*.md
ADVANCED_*.md
FINAL_*.md
IMPLEMENTATION_*.md
INTEGRATION_*.md

# 临时文本文件
*.txt
123.txt
456.txt

# API演示文件
api_demo
api_demo.go

# 迁移脚本
migrate_*.go

# ============ 保留的重要文件 ============
# 以下文件将被保留：
# - README.md
# - LICENSE
# - Makefile
# - go.mod
# - go.sum
# - Dockerfile
# - docker-compose.yml
# - config.example.yaml
# - 源代码文件 (cmd/, internal/, pkg/)
# - 模板文件 (templates/)
# - 示例文件 (examples/)
# - 迁移文件 (migrations/)

# ============ 强制包含某些文件 ============
!config.example.yaml
!README.md
!LICENSE
!Makefile
!go.mod
!go.sum
!Dockerfile
!docker-compose.yml