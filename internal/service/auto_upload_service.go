package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/model"
	"magnet-downloader/internal/websocket"
	"magnet-downloader/pkg/fileprocessor"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/streamhg"
	"magnet-downloader/pkg/streamtape"
	"magnet-downloader/pkg/telegram"

	"gorm.io/gorm"
)

// AutoUploadService 自动上传服务接口
type AutoUploadService interface {
	// 启动自动上传服务
	Start(ctx context.Context) error
	// 停止自动上传服务
	Stop() error
	// 手动触发扫描和上传
	TriggerScan() error
	// 获取服务状态
	GetStatus() *AutoUploadStatus
	// 获取上传统计
	GetStats() *AutoUploadStats
}

// AutoUploadStatus 自动上传服务状态
type AutoUploadStatus struct {
	Running        bool      `json:"running"`
	LastScanTime   time.Time `json:"last_scan_time"`
	NextScanTime   time.Time `json:"next_scan_time"`
	ScanInterval   int       `json:"scan_interval_minutes"`
	PendingUploads int       `json:"pending_uploads"`
	ActiveUploads  int       `json:"active_uploads"`
}

// AutoUploadStats 自动上传统计
type AutoUploadStats struct {
	TotalScans        int64     `json:"total_scans"`
	TotalUploads      int64     `json:"total_uploads"`
	SuccessfulUploads int64     `json:"successful_uploads"`
	FailedUploads     int64     `json:"failed_uploads"`
	TotalDataUploaded int64     `json:"total_data_uploaded"`
	LastUploadTime    time.Time `json:"last_upload_time"`
	UploadSuccessRate float64   `json:"upload_success_rate"`
}

// UploadResult 通用上传结果
type UploadResult struct {
	Success  bool   `json:"success"`
	URL      string `json:"url"`
	PlayURL  string `json:"play_url"`
	FileCode string `json:"file_code"`
	Size     int64  `json:"size"`
	Title    string `json:"title"`
	CanPlay  bool   `json:"can_play"`
	Error    string `json:"error,omitempty"`
}

// UploadTask 上传任务
type UploadTask struct {
	ID        string                  `json:"id"`
	VideoFile fileprocessor.VideoFile `json:"video_file"`
	TaskDir   string                  `json:"task_dir"` // 任务目录路径
	Status    string                  `json:"status"`
	Progress  float64                 `json:"progress"`
	StartTime time.Time               `json:"start_time"`
	EndTime   time.Time               `json:"end_time"`
	Error     string                  `json:"error,omitempty"`
	Result    *UploadResult           `json:"result,omitempty"`
}

// autoUploadService 自动上传服务实现
type autoUploadService struct {
	db               *gorm.DB
	websocketSvc     *websocket.Service
	streamTapeClient *streamtape.Client
	streamHGClient   *streamhg.Client
	telegramSvc      TelegramService
	videoSelector    *fileprocessor.VideoSelector
	config           *config.Config

	// 服务状态
	running bool
	ctx     context.Context
	cancel  context.CancelFunc
	mutex   sync.RWMutex

	// 配置参数
	downloadDir          string
	scanInterval         time.Duration
	maxConcurrentUploads int

	// 运行时状态
	status       *AutoUploadStatus
	stats        *AutoUploadStats
	pendingTasks []UploadTask
	activeTasks  map[string]*UploadTask
	taskMutex    sync.RWMutex

	// 最近完成的任务缓存（防止重复上传）
	recentlyCompleted map[string]time.Time
	recentlyMutex     sync.RWMutex

	// 上传控制
	uploadSemaphore chan struct{}
}

// NewAutoUploadService 创建自动上传服务
func NewAutoUploadService(
	db *gorm.DB,
	websocketSvc *websocket.Service,
	streamTapeClient *streamtape.Client,
	streamHGClient *streamhg.Client,
	telegramSvc TelegramService,
	cfg *config.Config,
) AutoUploadService {

	// 确定下载目录
	downloadDir := "/www/wwwroot/JAVAPI.COM/downloads" // 实际下载目录
	if envDir := os.Getenv("DOWNLOAD_DIR"); envDir != "" {
		downloadDir = envDir
	}

	service := &autoUploadService{
		db:               db,
		websocketSvc:     websocketSvc,
		streamTapeClient: streamTapeClient,
		streamHGClient:   streamHGClient,
		telegramSvc:      telegramSvc,
		videoSelector:    fileprocessor.NewVideoSelector(),
		config:           cfg,

		downloadDir:          downloadDir,
		scanInterval:         time.Duration(cfg.FileProcessing.AutoUpload.ScanInterval) * time.Minute,
		maxConcurrentUploads: cfg.FileProcessing.AutoUpload.MaxConcurrentUploads,

		status: &AutoUploadStatus{
			Running:      false,
			ScanInterval: cfg.FileProcessing.AutoUpload.ScanInterval,
		},
		stats:             &AutoUploadStats{},
		activeTasks:       make(map[string]*UploadTask),
		recentlyCompleted: make(map[string]time.Time),
	}

	// 创建上传信号量
	service.uploadSemaphore = make(chan struct{}, service.maxConcurrentUploads)

	return service
}

// Start 启动自动上传服务
func (s *autoUploadService) Start(ctx context.Context) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.running {
		return fmt.Errorf("自动上传服务已在运行")
	}

	s.ctx, s.cancel = context.WithCancel(ctx)
	s.running = true
	s.status.Running = true

	logger.Info("启动自动上传服务")

	// 启动扫描循环
	go s.scanLoop()

	// 启动定期清理任务（每小时执行一次）
	go s.cleanupLoop()

	return nil
}

// Stop 停止自动上传服务
func (s *autoUploadService) Stop() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if !s.running {
		return fmt.Errorf("自动上传服务未在运行")
	}

	logger.Info("停止自动上传服务")

	s.cancel()
	s.running = false
	s.status.Running = false

	// 等待所有活跃上传完成
	s.waitForActiveUploads()

	return nil
}

// TriggerScan 手动触发扫描
func (s *autoUploadService) TriggerScan() error {
	if !s.running {
		return fmt.Errorf("自动上传服务未在运行")
	}

	logger.Info("手动触发视频扫描")
	go s.performScan()

	return nil
}

// GetStatus 获取服务状态
func (s *autoUploadService) GetStatus() *AutoUploadStatus {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	status := *s.status
	status.PendingUploads = len(s.pendingTasks)
	status.ActiveUploads = len(s.activeTasks)

	if s.running && s.scanInterval > 0 {
		status.NextScanTime = status.LastScanTime.Add(s.scanInterval)
	}

	return &status
}

// GetStats 获取上传统计
func (s *autoUploadService) GetStats() *AutoUploadStats {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	stats := *s.stats

	// 计算成功率
	if stats.TotalUploads > 0 {
		stats.UploadSuccessRate = float64(stats.SuccessfulUploads) / float64(stats.TotalUploads) * 100
	}

	return &stats
}

// isFileInActiveUpload 检查文件是否已在活跃上传任务中
func (s *autoUploadService) isFileInActiveUpload(filePath string) bool {
	s.taskMutex.RLock()
	defer s.taskMutex.RUnlock()
	
	for _, task := range s.activeTasks {
		if task.VideoFile.Path == filePath {
			return true
		}
	}
	return false
}

// isRecentlyCompleted 检查文件是否最近刚完成上传
func (s *autoUploadService) isRecentlyCompleted(filePath string) bool {
	s.recentlyMutex.RLock()
	defer s.recentlyMutex.RUnlock()
	
	if completedTime, exists := s.recentlyCompleted[filePath]; exists {
		// 如果在最近30秒内完成，认为是最近完成的
		if time.Since(completedTime) < 30*time.Second {
			return true
		}
		// 清理过期的记录
		delete(s.recentlyCompleted, filePath)
	}
	return false
}

// markRecentlyCompleted 标记文件为最近完成
func (s *autoUploadService) markRecentlyCompleted(filePath string) {
	s.recentlyMutex.Lock()
	defer s.recentlyMutex.Unlock()
	
	s.recentlyCompleted[filePath] = time.Now()
	
	// 清理超过5分钟的旧记录
	cutoff := time.Now().Add(-5 * time.Minute)
	for path, completedTime := range s.recentlyCompleted {
		if completedTime.Before(cutoff) {
			delete(s.recentlyCompleted, path)
		}
	}
}

// isFileStillDownloading 检查文件是否还在下载中
func (s *autoUploadService) isFileStillDownloading(filePath string) bool {
	// 检查数据库中是否有正在下载的任务
	var downloadingCount int64
	s.db.Model(&model.DownloadTask{}).
		Where("save_path = ? AND status IN ('downloading', 'active', 'waiting', 'paused')", filePath).
		Count(&downloadingCount)
	
	if downloadingCount > 0 {
		return true
	}

	// 检查同目录下是否有其他分段文件还在下载
	dir := filepath.Dir(filePath)
	baseName := filepath.Base(filePath)
	
	// 检查是否是分段文件（如 mizd-464-1.mp4, mizd-464-2.mp4）
	if s.isSegmentedFile(baseName) {
		return s.hasDownloadingSegments(dir, baseName)
	}
	
	return false
}

// isSegmentedFile 检查是否是分段文件
func (s *autoUploadService) isSegmentedFile(fileName string) bool {
	// 检查文件名是否包含分段标识（如 -1, -2, -3）
	return strings.Contains(fileName, "-1.") || 
		   strings.Contains(fileName, "-2.") || 
		   strings.Contains(fileName, "-3.") ||
		   strings.Contains(fileName, "-4.") ||
		   strings.Contains(fileName, "-5.")
}

// hasDownloadingSegments 检查同目录下是否有其他分段还在下载
func (s *autoUploadService) hasDownloadingSegments(dir, fileName string) bool {
	// 提取基础文件名（去掉分段标识）
	baseName := s.extractBaseFileName(fileName)
	
	// 检查数据库中是否有相同基础名称的文件还在下载
	var downloadingCount int64
	s.db.Model(&model.DownloadTask{}).
		Where("save_path LIKE ? AND status IN ('downloading', 'active', 'waiting', 'paused')", 
			filepath.Join(dir, baseName)+"%").
		Count(&downloadingCount)
	
	return downloadingCount > 0
}

// extractBaseFileName 提取基础文件名（去掉分段标识）
func (s *autoUploadService) extractBaseFileName(fileName string) string {
	// 移除扩展名
	ext := filepath.Ext(fileName)
	nameWithoutExt := strings.TrimSuffix(fileName, ext)
	
	// 移除分段标识（如 -1, -2, -3）
	for i := 1; i <= 10; i++ {
		segmentSuffix := fmt.Sprintf("-%d", i)
		if strings.HasSuffix(nameWithoutExt, segmentSuffix) {
			return strings.TrimSuffix(nameWithoutExt, segmentSuffix)
		}
	}
	
	return nameWithoutExt
}

// isFileStableAndComplete 检查文件是否稳定和完整
func (s *autoUploadService) isFileStableAndComplete(filePath string) bool {
	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		logger.Debugf("文件不存在或无法访问: %s", filePath)
		return false
	}
	
	// 检查文件大小是否合理（大于100MB）
	if fileInfo.Size() < 100*1024*1024 {
		logger.Debugf("文件太小，可能不完整: %s (%.2f MB)", filePath, float64(fileInfo.Size())/(1024*1024))
		return false
	}
	
	// 检查文件修改时间是否稳定（最近5分钟内没有修改）
	if time.Since(fileInfo.ModTime()) < 5*time.Minute {
		logger.Debugf("文件最近被修改，可能还在下载: %s", filePath)
		return false
	}
	
	return true
}

// waitForTasksCompletionAndNotify 等待任务完成并发送通知
func (s *autoUploadService) waitForTasksCompletionAndNotify() {
	// 等待所有活跃任务完成
	for {
		s.taskMutex.RLock()
		activeCount := len(s.activeTasks)
		pendingCount := len(s.pendingTasks)
		s.taskMutex.RUnlock()
		
		if activeCount == 0 && pendingCount == 0 {
			break
		}
		
		time.Sleep(5 * time.Second) // 每5秒检查一次
	}
	
	// 发送Telegram统计通知
	if s.telegramSvc != nil && s.telegramSvc.IsEnabled() {
		stats := s.GetStats()
		
		telegramStats := &telegram.UploadStats{
			TotalScans:        stats.TotalScans,
			TotalUploads:      stats.TotalUploads,
			SuccessfulUploads: stats.SuccessfulUploads,
			FailedUploads:     stats.FailedUploads,
			TotalDataUploaded: stats.TotalDataUploaded,
			LastUploadTime:    stats.LastUploadTime,
			UploadSuccessRate: stats.UploadSuccessRate,
			Duration:          time.Since(s.status.LastScanTime),
		}
		
		err := s.telegramSvc.SendUploadCompleted(telegramStats)
		if err != nil {
			logger.Warnf("发送Telegram上传完成通知失败: %v", err)
		} else {
			logger.Info("Telegram上传完成通知发送成功")
		}
	}
}

// scanLoop 扫描循环
func (s *autoUploadService) scanLoop() {
	ticker := time.NewTicker(s.scanInterval)
	defer ticker.Stop()

	// 启动时立即执行一次扫描
	s.performScan()

	for {
		select {
		case <-s.ctx.Done():
			logger.Info("自动上传服务扫描循环退出")
			return
		case <-ticker.C:
			s.performScan()
		}
	}
}

// cleanupLoop 定期清理循环
func (s *autoUploadService) cleanupLoop() {
	// 每小时执行一次清理
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	// 启动时延迟5分钟后执行第一次清理
	time.Sleep(5 * time.Minute)
	s.cleanupCompletedFiles()

	for {
		select {
		case <-s.ctx.Done():
			logger.Info("自动清理循环退出")
			return
		case <-ticker.C:
			s.cleanupCompletedFiles()
		}
	}
}

// performScan 执行扫描
func (s *autoUploadService) performScan() {
	logger.Info("开始扫描下载目录寻找新视频")

	s.mutex.Lock()
	s.status.LastScanTime = time.Now()
	s.stats.TotalScans++
	s.mutex.Unlock()

	// 扫描视频文件
	videos, err := s.videoSelector.SelectVideosFromDirectory(s.downloadDir)
	if err != nil {
		logger.Errorf("扫描视频文件失败: %v", err)
		return
	}

	if len(videos) == 0 {
		logger.Info("没有找到需要上传的新视频")
		return
	}

	logger.Infof("找到 %d 个视频文件需要上传", len(videos))

	// 过滤已上传的视频
	newVideos := s.filterNewVideos(videos)
	if len(newVideos) == 0 {
		logger.Info("所有视频都已上传")
		return
	}

	logger.Infof("发现 %d 个新视频需要上传", len(newVideos))

	// 创建上传任务
	s.createUploadTasks(newVideos)

	// 启动上传处理
	s.processUploadTasks()
	
	// 等待所有任务完成后发送统计通知
	go s.waitForTasksCompletionAndNotify()
}

// filterNewVideos 过滤已上传的视频和正在下载的视频
func (s *autoUploadService) filterNewVideos(videos []fileprocessor.VideoFile) []fileprocessor.VideoFile {
	var newVideos []fileprocessor.VideoFile

	for _, video := range videos {
		// 1. 一次性检查数据库中该视频的所有相关记录
		var taskRecord model.DownloadTask
		result := s.db.Where("save_path = ?", video.Path).First(&taskRecord)

		if result.Error == nil {
			// 记录存在，检查状态
			hasPlayURL := taskRecord.PlayURL != "" && taskRecord.PlayURL != "null"
			isCompleted := taskRecord.Status == "completed" && taskRecord.ProcessingStatus == "completed"
			isFailed := taskRecord.Status == "failed" || taskRecord.ProcessingStatus == "failed"

			if hasPlayURL && isCompleted {
				// 🔥 优化：已成功上传且有播放链接的文件，主动删除以释放磁盘空间
				if s.config.FileProcessing.AutoUpload.DeleteAfterUpload {
					logger.Infof("发现已完成上传的文件，主动删除以释放空间: %s", video.Path)
					s.deleteCompletedFile(video.Path, &taskRecord)
				}
				continue
			}

			if hasPlayURL && isFailed {
				logger.Infof("发现失败但有播放链接的任务，将重新上传: %s", video.Path)
				// 不跳过，允许重新上传
			} else if !hasPlayURL && isFailed {
				// 纯失败记录，使用Debug级别减少日志
				continue
			}
		}
		// 如果记录不存在(result.Error != nil)，继续处理新文件

		// 2. 检查是否已经在活跃上传任务中
		if s.isFileInActiveUpload(video.Path) {
			logger.Debugf("视频正在上传中，跳过: %s", video.Path)
			continue
		}

		// 2.5. 检查是否最近刚完成上传（防止重复上传）
		if s.isRecentlyCompleted(video.Path) {
			logger.Debugf("视频最近刚完成上传，跳过: %s", video.Path)
			continue
		}

		// 3. 检查文件是否还在下载中（通过aria2状态）
		if s.isFileStillDownloading(video.Path) {
			logger.Debugf("视频仍在下载中，跳过: %s", video.Path)
			continue
		}

		// 4. 检查文件完整性和稳定性
		if !s.isFileStableAndComplete(video.Path) {
			logger.Debugf("视频文件不稳定或不完整，跳过: %s", video.Path)
			continue
		}

		newVideos = append(newVideos, video)
	}

	// 输出过滤统计信息
	skippedCount := len(videos) - len(newVideos)
	if skippedCount > 0 {
		logger.Infof("过滤完成: %d 个新视频需要上传，%d 个已跳过", len(newVideos), skippedCount)
	} else {
		logger.Infof("发现 %d 个新视频需要上传", len(newVideos))
	}

	return newVideos
}

// createUploadTasks 创建上传任务
func (s *autoUploadService) createUploadTasks(videos []fileprocessor.VideoFile) {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()

	for _, video := range videos {
		task := UploadTask{
			ID:        fmt.Sprintf("upload_%d", time.Now().UnixNano()),
			VideoFile: video,
			TaskDir:   video.TaskDir,
			Status:    "pending",
			Progress:  0,
		}

		s.pendingTasks = append(s.pendingTasks, task)
		logger.Infof("创建上传任务: %s -> %s (目录: %s)", task.ID, video.Name, video.TaskDir)
	}
}

// processUploadTasks 处理上传任务
func (s *autoUploadService) processUploadTasks() {
	s.taskMutex.RLock()
	pendingCount := len(s.pendingTasks)
	s.taskMutex.RUnlock()

	if pendingCount == 0 {
		return
	}

	logger.Infof("开始处理 %d 个上传任务", pendingCount)

	// 启动上传工作协程
	for i := 0; i < s.maxConcurrentUploads; i++ {
		go s.uploadWorker()
	}
}

// uploadWorker 上传工作协程（支持有限并发和延迟）
func (s *autoUploadService) uploadWorker() {
	for {
		// 获取下一个待上传任务
		task := s.getNextPendingTask()
		if task == nil {
			// 没有更多任务，退出
			return
		}

		// 获取上传信号量
		select {
		case s.uploadSemaphore <- struct{}{}:
			// 添加上传延迟（避免速率限制）
			if s.config.FileProcessing.AutoUpload.UploadDelaySeconds > 0 {
				delay := time.Duration(s.config.FileProcessing.AutoUpload.UploadDelaySeconds) * time.Second
				logger.Debugf("等待 %v 后开始上传 %s", delay, task.VideoFile.Name)
				time.Sleep(delay)
			}

			// 执行上传（带重试）
			s.executeUploadWithRetry(task)
			// 释放信号量
			<-s.uploadSemaphore
		case <-s.ctx.Done():
			// 服务被停止
			return
		}
	}
}

// getNextPendingTask 获取下一个待上传任务
func (s *autoUploadService) getNextPendingTask() *UploadTask {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()

	if len(s.pendingTasks) == 0 {
		return nil
	}

	// 取出第一个任务
	task := s.pendingTasks[0]
	s.pendingTasks = s.pendingTasks[1:]

	// 移动到活跃任务列表
	s.activeTasks[task.ID] = &task

	return &task
}

// executeUpload 执行上传
func (s *autoUploadService) executeUpload(task *UploadTask) {
	logger.Infof("开始上传视频: %s", task.VideoFile.Name)

	// 更新任务状态
	task.Status = "uploading"
	task.StartTime = time.Now()
	task.Progress = 0

	// 发送WebSocket通知
	s.notifyUploadStart(task)

	// 验证视频文件
	if err := s.videoSelector.ValidateVideoFile(task.VideoFile.Path); err != nil {
		s.handleUploadError(task, fmt.Errorf("视频文件验证失败: %w", err))
		return
	}

	// 根据配置的上传提供商执行上传
	switch s.config.FileProcessing.UploadProvider {
	case "streamtape":
		s.executeUploadToStreamTape(task)
	case "streamhg":
		s.executeUploadToStreamHG(task)
	case "dual":
		// dual模式：并行上传到StreamTape和StreamHG
		logger.Infof("Dual模式：并行上传到StreamTape和StreamHG %s", task.VideoFile.Name)
		s.executeUploadToDual(task)
	default:
		s.handleUploadError(task, fmt.Errorf("不支持的上传提供商: %s (支持: streamtape, streamhg, dual)", s.config.FileProcessing.UploadProvider))
	}
}

// executeUploadWithRetry 执行上传（带重试机制）
func (s *autoUploadService) executeUploadWithRetry(task *UploadTask) {
	// 更新任务状态
	task.Status = "uploading"
	task.StartTime = time.Now()
	task.Progress = 0

	// 发送WebSocket通知
	s.notifyUploadStart(task)

	// 验证视频文件
	if err := s.videoSelector.ValidateVideoFile(task.VideoFile.Path); err != nil {
		s.handleUploadError(task, fmt.Errorf("视频文件验证失败: %w", err))
		return
	}

	// 获取最大重试次数
	maxRetries := s.config.FileProcessing.AutoUpload.MaxRetriesPerFile
	if maxRetries <= 0 {
		maxRetries = 3 // 默认重试3次
	}

	// 重试上传
	for attempt := 1; attempt <= maxRetries; attempt++ {
		logger.Infof("上传尝试 %d/%d: %s", attempt, maxRetries, task.VideoFile.Name)

		// 🔥 修复：调用新的上传逻辑而不是直接调用DoodStream
		s.executeUpload(task)

		// 检查上传结果
		if task.Status == "completed" {
			logger.Infof("上传成功 (尝试 %d/%d): %s", attempt, maxRetries, task.VideoFile.Name)
			return
		}

	}

	// 所有重试都失败了
	if task.Status != "completed" {
		logger.Errorf("上传最终失败，已重试 %d 次: %s", maxRetries, task.VideoFile.Name)
		if task.Status != "failed" {
			// 如果任务状态还没有被设置为失败，手动设置
			s.handleUploadError(task, fmt.Errorf("上传失败，已重试 %d 次", maxRetries))
		}
	}
}

// isNonRetryableError 检查是否为不可重试的错误
func (s *autoUploadService) isNonRetryableError(err error) bool {
	if err == nil {
		return false
	}

	errStr := strings.ToLower(err.Error())

	// 不可重试的错误类型
	nonRetryableErrors := []string{
		"not a video file",          // 文件格式错误
		"file format not supported", // 文件格式不支持
		"invalid file type",         // 无效文件类型
		"file corrupted",            // 文件损坏
		"authentication failed",     // 认证失败
		"api key invalid",           // API密钥无效
		"forbidden",                 // 禁止访问
		"file too large",            // 文件过大（平台限制）
		"quota exceeded",            // 配额超出
		"account suspended",         // 账户被暂停
	}

	for _, nonRetryable := range nonRetryableErrors {
		if strings.Contains(errStr, nonRetryable) {
			return true
		}
	}

	return false
}

// handleUploadSuccess 处理上传成功
func (s *autoUploadService) handleUploadSuccess(task *UploadTask, result *UploadResult) {
	task.Status = "completed"
	task.Progress = 100
	task.EndTime = time.Now()
	task.Result = result

	logger.Infof("视频上传成功: %s -> %s", task.VideoFile.Name, result.PlayURL)

	// 更新统计
	s.mutex.Lock()
	s.stats.TotalUploads++
	s.stats.SuccessfulUploads++
	s.stats.TotalDataUploaded += task.VideoFile.Size
	s.stats.LastUploadTime = time.Now()
	s.mutex.Unlock()

	// 保存到数据库
	s.saveUploadResult(task, result)

	// 标记为最近完成（防止重复上传）
	s.markRecentlyCompleted(task.VideoFile.Path)

	// 检查是否需要删除任务目录
	if s.config.FileProcessing.AutoUpload.DeleteAfterUpload {
		s.deleteTaskDirectoryIfComplete(task.TaskDir)
	}

	// 发送WebSocket通知
	s.notifyUploadComplete(task)

	// 发送Telegram通知
	if s.telegramSvc != nil && s.telegramSvc.IsEnabled() {
		// 这里可以发送单个文件上传成功通知，或者累积统计后发送
		// 为了避免过多通知，我们在扫描完成后发送统计通知
		logger.Debugf("上传成功，将在批次完成后发送Telegram统计通知")
	}

	// 从活跃任务中移除
	s.removeActiveTask(task.ID)
}

// handleUploadError 处理上传错误
func (s *autoUploadService) handleUploadError(task *UploadTask, err error) {
	task.Status = "failed"
	task.EndTime = time.Now()
	task.Error = err.Error()

	logger.Errorf("视频上传失败: %s -> %v", task.VideoFile.Name, err)

	// 记录失败的文件到数据库，避免重复尝试
	s.recordFailedUpload(task, err)

	// 更新统计
	s.mutex.Lock()
	s.stats.TotalUploads++
	s.stats.FailedUploads++
	s.mutex.Unlock()

	// 发送WebSocket通知
	s.notifyUploadError(task)

	// 发送Telegram失败通知
	if s.telegramSvc != nil && s.telegramSvc.IsEnabled() {
		provider := s.config.FileProcessing.UploadProvider
		if provider == "dual" {
			provider = "StreamTape/StreamHG"
		}
		
		telegramErr := s.telegramSvc.SendUploadFailed(task.VideoFile.Name, provider, task.Error)
		if telegramErr != nil {
			logger.Warnf("发送Telegram上传失败通知失败: %v", telegramErr)
		}
	}

	// 从活跃任务中移除
	s.removeActiveTask(task.ID)
}

// recordFailedUpload 记录失败的上传，避免重复尝试
func (s *autoUploadService) recordFailedUpload(task *UploadTask, err error) {
	// 查找或创建下载任务记录
	var downloadTask model.DownloadTask

	// 尝试根据文件路径查找现有任务
	result := s.db.Where("save_path = ?", task.VideoFile.Path).First(&downloadTask)

	if result.Error != nil {
		// 创建新的失败记录
		downloadTask = model.DownloadTask{
			TaskName:         task.VideoFile.Name,
			Status:           "failed",
			SavePath:         task.VideoFile.Path,
			TotalSize:        task.VideoFile.Size,
			ErrorMessage:     err.Error(),
			UserID:           1, // 默认用户ID
			CompletedAt:      &task.EndTime,
			ProcessingStatus: "failed",
		}

		if createErr := s.db.Create(&downloadTask).Error; createErr != nil {
			logger.Errorf("记录失败上传到数据库失败: %v", createErr)
		} else {
			logger.Infof("已记录失败上传: %s", task.VideoFile.Name)
		}
	} else {
		// 检查是否已有播放链接，如果有则不覆盖成功记录
		if downloadTask.PlayURL != "" && downloadTask.PlayURL != "null" {
			logger.Warnf("任务已有播放链接，不覆盖成功记录: %s (URL: %s)", task.VideoFile.Name, downloadTask.PlayURL)
			logger.Warnf("错误信息仅记录到日志: %s", err.Error())
			return
		}

		// 更新现有记录为失败状态（仅当没有播放链接时）
		downloadTask.Status = "failed"
		downloadTask.ErrorMessage = err.Error()
		downloadTask.ProcessingStatus = "failed"
		downloadTask.CompletedAt = &task.EndTime

		if updateErr := s.db.Save(&downloadTask).Error; updateErr != nil {
			logger.Errorf("更新失败上传记录失败: %v", updateErr)
		} else {
			logger.Infof("已更新失败上传记录: %s", task.VideoFile.Name)
		}
	}
}

// saveUploadResult 保存上传结果到数据库
func (s *autoUploadService) saveUploadResult(task *UploadTask, result *UploadResult) {
	// 查找或创建下载任务记录
	var downloadTask model.DownloadTask

	// 尝试根据文件路径查找现有任务
	err := s.db.Where("save_path = ?", task.VideoFile.Path).First(&downloadTask).Error
	if err == gorm.ErrRecordNotFound {
		// 创建新的任务记录
		downloadTask = model.DownloadTask{
			TaskName:         task.VideoFile.Name,
			SavePath:         task.VideoFile.Path,
			TotalSize:        task.VideoFile.Size,
			Status:           model.TaskStatusCompleted,
			ProcessingStatus: "completed",
			PlayURL:          result.PlayURL,
			ShareCode:        result.FileCode,
			CompletedAt:      &task.EndTime,
			UserID:           1, // 默认用户ID，实际应用中应该从上下文获取
		}

		if err := s.db.Create(&downloadTask).Error; err != nil {
			logger.Errorf("创建下载任务记录失败: %v", err)
		}
	} else if err == nil {
		// 更新现有任务记录
		updates := map[string]interface{}{
			"play_url":          result.PlayURL,
			"share_code":        result.FileCode,
			"total_size":        task.VideoFile.Size,
			"processing_status": "completed",
			"status":            model.TaskStatusCompleted,
			"completed_at":      task.EndTime,
		}

		if err := s.db.Model(&downloadTask).Updates(updates).Error; err != nil {
			logger.Errorf("更新下载任务记录失败: %v", err)
		}
	} else {
		logger.Errorf("查询下载任务记录失败: %v", err)
	}
}

// removeActiveTask 从活跃任务中移除
func (s *autoUploadService) removeActiveTask(taskID string) {
	s.taskMutex.Lock()
	defer s.taskMutex.Unlock()

	delete(s.activeTasks, taskID)
}

// waitForActiveUploads 等待所有活跃上传完成
func (s *autoUploadService) waitForActiveUploads() {
	logger.Info("等待活跃上传任务完成...")

	for {
		s.taskMutex.RLock()
		activeCount := len(s.activeTasks)
		s.taskMutex.RUnlock()

		if activeCount == 0 {
			break
		}

		logger.Infof("还有 %d 个活跃上传任务，等待完成...", activeCount)
		time.Sleep(2 * time.Second)
	}

	logger.Info("所有活跃上传任务已完成")
}

// WebSocket通知方法
func (s *autoUploadService) notifyUploadStart(task *UploadTask) {
	if s.websocketSvc != nil {
		s.websocketSvc.BroadcastSystemNotification(
			"自动上传开始",
			fmt.Sprintf("开始上传视频: %s", task.VideoFile.Name),
			"info",
		)
	}
}

func (s *autoUploadService) notifyUploadComplete(task *UploadTask) {
	if s.websocketSvc != nil {
		s.websocketSvc.BroadcastSystemNotification(
			"自动上传完成",
			fmt.Sprintf("视频 %s 上传成功", task.VideoFile.Name),
			"success",
		)
	}
}

func (s *autoUploadService) notifyUploadError(task *UploadTask) {
	if s.websocketSvc != nil {
		s.websocketSvc.BroadcastSystemNotification(
			"自动上传失败",
			fmt.Sprintf("视频 %s 上传失败: %s", task.VideoFile.Name, task.Error),
			"error",
		)
	}
}

// deleteTaskDirectoryIfComplete 检查任务目录是否所有视频都已上传完成，如果是则删除目录
func (s *autoUploadService) deleteTaskDirectoryIfComplete(taskDir string) {
	if taskDir == "" {
		return
	}

	// 🚨 安全检查：跳过根目录文件的删除（根目录文件使用特殊TaskDir标识）
	if strings.HasSuffix(taskDir, "/__ROOT__") {
		logger.Debugf("跳过根目录文件的删除检查: %s", taskDir)
		return
	}

	// 🚨 安全检查：禁止删除downloads根目录本身
	if taskDir == s.downloadDir {
		logger.Warnf("拒绝删除downloads根目录: %s", taskDir)
		return
	}

	logger.Infof("检查任务目录是否可以删除: %s", taskDir)

	// 检查该目录是否还有其他待上传或正在上传的任务
	s.taskMutex.RLock()
	hasActiveTasks := false
	for _, task := range s.activeTasks {
		if task.TaskDir == taskDir && task.Status != "completed" {
			hasActiveTasks = true
			break
		}
	}

	for _, task := range s.pendingTasks {
		if task.TaskDir == taskDir {
			hasActiveTasks = true
			break
		}
	}
	s.taskMutex.RUnlock()

	if hasActiveTasks {
		logger.Infof("任务目录 %s 还有未完成的上传任务，暂不删除", taskDir)
		return
	}

	// 检查数据库中该目录的所有视频是否都已上传
	var unuploadedCount int64
	s.db.Model(&model.DownloadTask{}).
		Where("save_path LIKE ? AND (play_url IS NULL OR play_url = '')", taskDir+"/%").
		Count(&unuploadedCount)

	if unuploadedCount > 0 {
		logger.Infof("任务目录 %s 还有 %d 个未上传的视频，暂不删除", taskDir, unuploadedCount)
		return
	}

	// 删除任务目录
	logger.Infof("开始删除任务目录: %s", taskDir)
	if err := s.deleteDirectory(taskDir); err != nil {
		logger.Errorf("删除任务目录失败 %s: %v", taskDir, err)

		// 发送删除失败通知
		if s.websocketSvc != nil {
			s.websocketSvc.BroadcastSystemNotification(
				"目录删除失败",
				fmt.Sprintf("无法删除目录 %s: %v", taskDir, err),
				"warning",
			)
		}
	} else {
		logger.Infof("成功删除任务目录: %s", taskDir)

		// 发送删除成功通知
		if s.websocketSvc != nil {
			s.websocketSvc.BroadcastSystemNotification(
				"目录清理完成",
				fmt.Sprintf("已删除完成的任务目录: %s", taskDir),
				"success",
			)
		}
	}
}

// deleteDirectory 安全删除目录
func (s *autoUploadService) deleteDirectory(dirPath string) error {
	// 安全检查：确保只删除下载目录下的子目录
	if !strings.HasPrefix(dirPath, s.downloadDir) {
		return fmt.Errorf("拒绝删除下载目录外的路径: %s", dirPath)
	}

	// 🚨 重要安全检查：禁止删除downloads根目录本身
	if dirPath == s.downloadDir {
		logger.Warnf("拒绝删除downloads根目录: %s", dirPath)
		return fmt.Errorf("禁止删除downloads根目录: %s", dirPath)
	}

	// 检查目录是否存在
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		logger.Infof("目录已不存在，跳过删除: %s", dirPath)
		return nil
	}

	// 获取目录大小信息（用于日志）
	dirSize, fileCount := s.getDirectoryInfo(dirPath)

	// 删除目录
	err := os.RemoveAll(dirPath)
	if err != nil {
		return fmt.Errorf("删除目录失败: %w", err)
	}

	logger.Infof("目录删除成功: %s (释放空间: %.2f GB, 文件数: %d)",
		dirPath, float64(dirSize)/(1024*1024*1024), fileCount)

	return nil
}

// getDirectoryInfo 获取目录信息
func (s *autoUploadService) getDirectoryInfo(dirPath string) (int64, int) {
	var totalSize int64
	var fileCount int

	filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		if !info.IsDir() {
			totalSize += info.Size()
			fileCount++
		}
		return nil
	})

	return totalSize, fileCount
}

// deleteCompletedFile 删除已完成上传的文件
func (s *autoUploadService) deleteCompletedFile(filePath string, taskRecord *model.DownloadTask) {
	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		logger.Debugf("文件已不存在，无需删除: %s", filePath)
		return
	}
	if err != nil {
		logger.Errorf("检查文件状态失败: %s -> %v", filePath, err)
		return
	}

	// 记录文件大小用于统计
	fileSize := fileInfo.Size()
	fileSizeGB := float64(fileSize) / (1024 * 1024 * 1024)

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		logger.Errorf("删除已完成文件失败: %s -> %v", filePath, err)
		return
	}

	logger.Infof("✅ 已完成文件删除成功: %s (释放空间: %.2f GB)", filePath, fileSizeGB)

	// 发送WebSocket通知
	if s.websocketSvc != nil {
		s.websocketSvc.BroadcastSystemNotification(
			"文件清理完成",
			fmt.Sprintf("已删除完成上传的文件: %s (释放 %.2f GB)", filepath.Base(filePath), fileSizeGB),
			"success",
		)
	}

	// 检查是否需要删除空目录
	dirPath := filepath.Dir(filePath)
	if dirPath != s.downloadDir { // 不删除根下载目录
		s.deleteEmptyDirectory(dirPath)
	}

	// 更新统计
	s.mutex.Lock()
	s.stats.TotalDataUploaded += fileSize // 累计已处理的数据量
	s.mutex.Unlock()
}

// cleanupCompletedFiles 定期清理已完成上传的文件
func (s *autoUploadService) cleanupCompletedFiles() {
	if !s.config.FileProcessing.AutoUpload.DeleteAfterUpload {
		logger.Debug("文件删除功能未启用，跳过清理")
		return
	}

	logger.Info("开始清理已完成上传的文件...")

	// 查询所有已完成且有播放链接的任务
	var completedTasks []model.DownloadTask
	err := s.db.Where("status = ? AND processing_status = ? AND (play_url IS NOT NULL AND play_url != '')", 
		"completed", "completed").Find(&completedTasks).Error
	
	if err != nil {
		logger.Errorf("查询已完成任务失败: %v", err)
		return
	}

	cleanedCount := 0
	totalSizeFreed := int64(0)

	for _, task := range completedTasks {
		if task.SavePath == "" {
			continue
		}

		// 检查文件是否存在
		fileInfo, err := os.Stat(task.SavePath)
		if os.IsNotExist(err) {
			continue // 文件已不存在
		}
		if err != nil {
			logger.Errorf("检查文件状态失败: %s -> %v", task.SavePath, err)
			continue
		}

		// 删除文件
		fileSize := fileInfo.Size()
		if err := os.Remove(task.SavePath); err != nil {
			logger.Errorf("删除文件失败: %s -> %v", task.SavePath, err)
			continue
		}

		cleanedCount++
		totalSizeFreed += fileSize
		logger.Infof("清理完成文件: %s (%.2f GB)", task.SavePath, float64(fileSize)/(1024*1024*1024))

		// 检查是否需要删除空目录
		dirPath := filepath.Dir(task.SavePath)
		if dirPath != s.downloadDir {
			s.deleteEmptyDirectory(dirPath)
		}
	}

	if cleanedCount > 0 {
		totalSizeGB := float64(totalSizeFreed) / (1024 * 1024 * 1024)
		logger.Infof("✅ 清理完成: 删除 %d 个文件，释放 %.2f GB 空间", cleanedCount, totalSizeGB)
		
		// 发送通知
		if s.websocketSvc != nil {
			s.websocketSvc.BroadcastSystemNotification(
				"批量清理完成",
				fmt.Sprintf("已清理 %d 个完成上传的文件，释放 %.2f GB 空间", cleanedCount, totalSizeGB),
				"success",
			)
		}
	} else {
		logger.Info("没有需要清理的文件")
	}
}
