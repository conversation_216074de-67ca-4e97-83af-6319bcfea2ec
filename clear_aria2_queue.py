#!/usr/bin/env python3
import requests
import json

def clear_aria2_queue():
    url = "http://localhost:6800/jsonrpc"
    headers = {"Content-Type": "application/json"}
    
    # 获取等待队列
    payload = {
        "jsonrpc": "2.0",
        "method": "aria2.tellWaiting",
        "params": ["token:aria2secret", 0, 1000],
        "id": "1"
    }
    
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    data = response.json()
    
    if 'result' in data:
        waiting_tasks = data['result']
        print(f"📊 发现 {len(waiting_tasks)} 个等待任务")
        
        # 移除每个等待任务
        removed_count = 0
        for task in waiting_tasks:
            gid = task.get('gid')
            if gid:
                remove_payload = {
                    "jsonrpc": "2.0",
                    "method": "aria2.remove",
                    "params": ["token:aria2secret", gid],
                    "id": "1"
                }
                
                remove_response = requests.post(url, headers=headers, data=json.dumps(remove_payload))
                remove_data = remove_response.json()
                
                if 'result' in remove_data:
                    removed_count += 1
                    if removed_count % 10 == 0:
                        print(f"  已移除 {removed_count} 个任务...")
        
        print(f"✅ 成功移除 {removed_count} 个等待任务")
    else:
        print("获取等待任务失败:", data)

if __name__ == "__main__":
    clear_aria2_queue()