# 🧲 磁力链接转直链工具

基于webtor.io等多个在线服务，将磁力链接转换为直接下载链接的Linux工具。

## ✨ 功能特点

- 🎯 **自动监听剪贴板** - 复制磁力链接即可自动处理
- 🌐 **多服务支持** - 集成5个主流在线转换服务
- 📋 **自动复制链接** - 处理结果自动复制到剪贴板
- 🔧 **命令行友好** - 支持直接命令行调用
- 🚀 **即开即用** - 无需复杂配置

## 🛠️ 支持的服务

1. **Webtor.io** - 在线播放和下载
2. **Instant.io** - 即时下载和流媒体
3. **Seedr.cc** - 云端下载服务
4. **Put.io** - 云存储下载
5. **BTCache.me** - Torrent缓存和下载

## 📦 安装

工具已经安装在系统中，可以直接使用：

```bash
# 检查是否安装
which magnet2direct
```

## 🚀 使用方法

### 方法一：剪贴板监听模式（推荐）

```bash
# 启动监听器
magnet2direct

# 然后复制任何磁力链接到剪贴板，工具会自动处理
```

### 方法二：直接转换模式

```bash
# 直接转换磁力链接
magnet2direct "magnet:?xt=urn:btih:9866D7F7256C520DE0A495F1945EEDDF1D3136B9&dn=embz-325"
```

### 方法三：Python脚本调用

```bash
# 使用测试版本
python3 /www/wwwroot/JAVAPI.COM/magnet_test.py "磁力链接"

# 使用剪贴板监听版本
python3 /www/wwwroot/JAVAPI.COM/magnet_clipboard_monitor.py
```

## 📖 使用流程

1. **启动工具**
   ```bash
   magnet2direct
   ```

2. **复制磁力链接**
   - 从任何地方复制磁力链接到剪贴板
   - 工具会自动检测并处理

3. **获取转换结果**
   - 工具显示多个在线服务链接
   - 第一个链接自动复制到剪贴板

4. **访问在线服务**
   - 粘贴链接到浏览器打开
   - 等待torrent解析完成
   - 选择要下载的文件
   - 获取直接下载链接

## 🎯 示例输出

```
🧲 检测到磁力链接!
============================================================
🔑 Hash: 9866D7F7256C520DE0A495F1945EEDDF1D3136B9
📁 名称: embz-325
📋 磁力链接: magnet:?xt=urn:btih:9866D7F7256C520DE0A495F1945EEDDF1D3136B9...

🎯 可用的在线服务:
--------------------------------------------------
1. Webtor.io
   🌐 https://webtor.io/?magnet=magnet%3A%3Fxt%3Durn%3Abtih%3A...
   📝 在线播放和下载

2. Instant.io
   🌐 https://instant.io/#magnet:?xt=urn:btih:9866D7F7256C520DE0A495F1945EEDDF1D3136B9...
   📝 即时下载和流媒体

📋 已将 Webtor.io 链接复制到剪贴板
```

## 🔧 技术实现

- **剪贴板监听**: 使用 `xclip` 监听X11剪贴板
- **磁力链接解析**: 正则表达式提取hash和文件名
- **URL编码**: 自动处理特殊字符编码
- **多服务集成**: 支持5个主流在线转换服务

## 📁 文件结构

```
/www/wwwroot/JAVAPI.COM/
├── magnet_test.py                 # 简单测试版本
├── magnet_clipboard_monitor.py    # 剪贴板监听版本
├── magnet_converter_advanced.py   # 高级版本（包含API调用）
└── README_magnet2direct.md        # 使用说明

/usr/local/bin/
└── magnet2direct                  # 命令行工具
```

## ⚠️ 注意事项

1. **依赖要求**
   - 需要 `xclip` 用于剪贴板操作
   - 需要 Python 3.6+ 环境
   - 需要网络连接访问在线服务

2. **服务限制**
   - 某些服务可能需要注册账号
   - 免费账号可能有下载限制
   - 服务可用性可能因地区而异

3. **隐私安全**
   - 磁力链接会发送到第三方服务
   - 建议使用VPN保护隐私
   - 注意遵守当地法律法规

## 🆘 故障排除

### 剪贴板不工作
```bash
# 检查xclip是否安装
which xclip

# 检查DISPLAY环境变量
echo $DISPLAY

# 手动设置DISPLAY
export DISPLAY=:1
```

### 网络连接问题
```bash
# 测试网络连接
curl -I https://webtor.io

# 使用代理（如果需要）
export http_proxy=http://proxy:port
export https_proxy=http://proxy:port
```

## 🔄 更新日志

- **v1.0** - 基础功能实现
- **v1.1** - 添加多服务支持
- **v1.2** - 优化剪贴板监听
- **v1.3** - 添加自动复制功能

## 📞 支持

如有问题或建议，请联系系统管理员或查看日志文件。

---

**🎉 享受便捷的磁力链接转换体验！**